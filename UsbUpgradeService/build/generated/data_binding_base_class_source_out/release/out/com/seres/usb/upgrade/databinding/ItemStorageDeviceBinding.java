// Generated by view binder compiler. Do not edit!
package com.seres.usb.upgrade.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.seres.usb.upgrade.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemStorageDeviceBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final Button btnOpenDevice;

  @NonNull
  public final ProgressBar progressBarUsage;

  @NonNull
  public final TextView tvDeviceName;

  @NonNull
  public final TextView tvDevicePath;

  @NonNull
  public final TextView tvDeviceStatus;

  @NonNull
  public final TextView tvStorageInfo;

  @NonNull
  public final TextView tvUsagePercentage;

  private ItemStorageDeviceBinding(@NonNull CardView rootView, @NonNull Button btnOpenDevice,
      @NonNull ProgressBar progressBarUsage, @NonNull TextView tvDeviceName,
      @NonNull TextView tvDevicePath, @NonNull TextView tvDeviceStatus,
      @NonNull TextView tvStorageInfo, @NonNull TextView tvUsagePercentage) {
    this.rootView = rootView;
    this.btnOpenDevice = btnOpenDevice;
    this.progressBarUsage = progressBarUsage;
    this.tvDeviceName = tvDeviceName;
    this.tvDevicePath = tvDevicePath;
    this.tvDeviceStatus = tvDeviceStatus;
    this.tvStorageInfo = tvStorageInfo;
    this.tvUsagePercentage = tvUsagePercentage;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemStorageDeviceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemStorageDeviceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_storage_device, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemStorageDeviceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnOpenDevice;
      Button btnOpenDevice = ViewBindings.findChildViewById(rootView, id);
      if (btnOpenDevice == null) {
        break missingId;
      }

      id = R.id.progressBarUsage;
      ProgressBar progressBarUsage = ViewBindings.findChildViewById(rootView, id);
      if (progressBarUsage == null) {
        break missingId;
      }

      id = R.id.tvDeviceName;
      TextView tvDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceName == null) {
        break missingId;
      }

      id = R.id.tvDevicePath;
      TextView tvDevicePath = ViewBindings.findChildViewById(rootView, id);
      if (tvDevicePath == null) {
        break missingId;
      }

      id = R.id.tvDeviceStatus;
      TextView tvDeviceStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceStatus == null) {
        break missingId;
      }

      id = R.id.tvStorageInfo;
      TextView tvStorageInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvStorageInfo == null) {
        break missingId;
      }

      id = R.id.tvUsagePercentage;
      TextView tvUsagePercentage = ViewBindings.findChildViewById(rootView, id);
      if (tvUsagePercentage == null) {
        break missingId;
      }

      return new ItemStorageDeviceBinding((CardView) rootView, btnOpenDevice, progressBarUsage,
          tvDeviceName, tvDevicePath, tvDeviceStatus, tvStorageInfo, tvUsagePercentage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
