// Generated by view binder compiler. Do not edit!
package com.seres.usb.upgrade.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.seres.usb.upgrade.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnClearLogs;

  @NonNull
  public final Button btnOpenUsb;

  @NonNull
  public final Button btnRefreshStorage;

  @NonNull
  public final Button btnShowActiveTasks;

  @NonNull
  public final Button btnStartService;

  @NonNull
  public final Button btnStopService;

  @NonNull
  public final RecyclerView recyclerViewStorageDevices;

  @NonNull
  public final TextView tvServiceStatus;

  @NonNull
  public final TextView tvStorageStatus;

  @NonNull
  public final TextView tvTaskCount;

  @NonNull
  public final TextView tvUsbStatus;

  private ActivityMainBinding(@NonNull ScrollView rootView, @NonNull Button btnClearLogs,
      @NonNull Button btnOpenUsb, @NonNull Button btnRefreshStorage,
      @NonNull Button btnShowActiveTasks, @NonNull Button btnStartService,
      @NonNull Button btnStopService, @NonNull RecyclerView recyclerViewStorageDevices,
      @NonNull TextView tvServiceStatus, @NonNull TextView tvStorageStatus,
      @NonNull TextView tvTaskCount, @NonNull TextView tvUsbStatus) {
    this.rootView = rootView;
    this.btnClearLogs = btnClearLogs;
    this.btnOpenUsb = btnOpenUsb;
    this.btnRefreshStorage = btnRefreshStorage;
    this.btnShowActiveTasks = btnShowActiveTasks;
    this.btnStartService = btnStartService;
    this.btnStopService = btnStopService;
    this.recyclerViewStorageDevices = recyclerViewStorageDevices;
    this.tvServiceStatus = tvServiceStatus;
    this.tvStorageStatus = tvStorageStatus;
    this.tvTaskCount = tvTaskCount;
    this.tvUsbStatus = tvUsbStatus;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnClearLogs;
      Button btnClearLogs = ViewBindings.findChildViewById(rootView, id);
      if (btnClearLogs == null) {
        break missingId;
      }

      id = R.id.btnOpenUsb;
      Button btnOpenUsb = ViewBindings.findChildViewById(rootView, id);
      if (btnOpenUsb == null) {
        break missingId;
      }

      id = R.id.btnRefreshStorage;
      Button btnRefreshStorage = ViewBindings.findChildViewById(rootView, id);
      if (btnRefreshStorage == null) {
        break missingId;
      }

      id = R.id.btnShowActiveTasks;
      Button btnShowActiveTasks = ViewBindings.findChildViewById(rootView, id);
      if (btnShowActiveTasks == null) {
        break missingId;
      }

      id = R.id.btnStartService;
      Button btnStartService = ViewBindings.findChildViewById(rootView, id);
      if (btnStartService == null) {
        break missingId;
      }

      id = R.id.btnStopService;
      Button btnStopService = ViewBindings.findChildViewById(rootView, id);
      if (btnStopService == null) {
        break missingId;
      }

      id = R.id.recyclerViewStorageDevices;
      RecyclerView recyclerViewStorageDevices = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewStorageDevices == null) {
        break missingId;
      }

      id = R.id.tvServiceStatus;
      TextView tvServiceStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvServiceStatus == null) {
        break missingId;
      }

      id = R.id.tvStorageStatus;
      TextView tvStorageStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStorageStatus == null) {
        break missingId;
      }

      id = R.id.tvTaskCount;
      TextView tvTaskCount = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskCount == null) {
        break missingId;
      }

      id = R.id.tvUsbStatus;
      TextView tvUsbStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvUsbStatus == null) {
        break missingId;
      }

      return new ActivityMainBinding((ScrollView) rootView, btnClearLogs, btnOpenUsb,
          btnRefreshStorage, btnShowActiveTasks, btnStartService, btnStopService,
          recyclerViewStorageDevices, tvServiceStatus, tvStorageStatus, tvTaskCount, tvUsbStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
