-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:86:9-94:20
	android:grantUriPermissions
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:90:13-47
	android:authorities
		INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:88:13-64
	android:exported
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:89:13-37
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:87:13-62
manifest
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:2:1-98:12
INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:2:1-98:12
INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:2:1-98:12
INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:2:1-98:12
MERGED from [androidx.databinding:viewbinding:8.6.0] /home/<USER>/.gradle/caches/8.9/transforms/146863973234875abc30cdcc92af2b0c/transformed/viewbinding-8.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [:s2s-sdk-api] /home/<USER>/dds/s2sService/s2s-sdk-api/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.10.0] /home/<USER>/.gradle/caches/8.9/transforms/2816070cc58ee779d6eda324d2c0a6ce/transformed/material-1.10.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /home/<USER>/.gradle/caches/8.9/transforms/713b6bd2b8c3ac657019a5a1a07d83ab/transformed/constraintlayout-2.1.4/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /home/<USER>/.gradle/caches/8.9/transforms/83d01cdcfc97a9ee1fa9cc750528eec7/transformed/appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] /home/<USER>/.gradle/caches/8.9/transforms/16178b8cf6033f49fe8192ba1281dfae/transformed/appcompat-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /home/<USER>/.gradle/caches/8.9/transforms/e613dbf6633ca10a897becde81915dea/transformed/recyclerview-1.3.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] /home/<USER>/.gradle/caches/8.9/transforms/83955a3621cb4fdfaa7dc6b5ec8ff01f/transformed/viewpager2-1.1.0-beta02/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.3.6] /home/<USER>/.gradle/caches/8.9/transforms/cc45b9ac07c9a4e553b7e37f5d5d866f/transformed/fragment-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/0d68aef391a1bef8cf69c14377ecc7db/transformed/activity-1.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/853784ca09e9924abb5958855432070b/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/20f2b4e8c0e7c2394a7da189c0ea435b/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/cc0cf4df6578d734ca157e2604285932/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/49db75becd20ccde1c6547fe074d871b/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/29a1cbc1498aa2d2dc598ea4aef7531e/transformed/transition-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/c41ae0981c4e37b7f6687f6266df5d19/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/a793849a76dabef9fb07a79cb2b4eca7/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/48d3072d62b6e14d8958dffdbfca9eb0/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/f0be03a2227e3e5e9a725862189d13a2/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/df2cf72cc21e0b25a43581f7211b4825/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/36206f3ca5f1efcd5d76add3849a6416/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /home/<USER>/.gradle/caches/8.9/transforms/156de66f05358e44cfb2fa9bde8c9f29/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/36e3f2de08975dc5e32f5ba858d74418/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/170ebf14fe6c2628397944d746c89cee/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/44823380f01d3b61034ea4642f0a606c/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/79c0fd544c62f74a7a7e75812eb06a1a/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/f7f1e67d3a1d990fd27c335eb234d635/transformed/lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/912bdbf86f2221318b1f11a74465cffc/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/0e780b3bcd3646138bc1bd95d093899c/transformed/core-ktx-1.10.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/40e363994e21beada7604a54f43c3c7e/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/1335f47639b9d8ad8ede95e4cbc029ec/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/6bff64edba13d70e91a489be5347be91/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/dddd54656ef41b816f6060c4df8f6ec3/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/dab1e70fbc520ac05302103961983552/transformed/tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/ac5bc8f2471abf6cbff1c9ebdc2d0848/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/c2da84da3fff6bb6ca84b344e0d148e9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /home/<USER>/.gradle/caches/8.9/transforms/fde77f2acb4a205ab10bd3079ebd36ae/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/5020891c53660ca6d738d1966fcc7307/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/6462eb6f120c2c633a0264aa5c519b5b/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/13a692efac45ce84e3c1717540fe168c/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:6:5-81
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:6:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:7:5-77
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:7:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:8:5-87
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:8:22-84
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:12:9-35
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:11:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:13:5-14:38
	android:maxSdkVersion
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:14:9-35
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:13:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:15:5-16:40
	tools:ignore
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:16:9-37
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:15:22-79
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:19:5-76
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:19:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:20:5-75
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:20:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:21:5-75
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:21:22-72
uses-permission#android.permission.MOUNT_UNMOUNT_FILESYSTEMS
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:24:5-25:47
	tools:ignore
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:25:9-44
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:24:22-81
uses-permission#android.hardware.usb.host
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:26:5-65
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:26:22-62
uses-permission#android.permission.INTERNET
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:27:5-67
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:27:22-64
uses-feature#android.hardware.usb.host
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:30:5-87
	android:required
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:30:60-84
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:30:19-59
application
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:32:5-96:19
INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:32:5-96:19
MERGED from [com.google.android.material:material:1.10.0] /home/<USER>/.gradle/caches/8.9/transforms/2816070cc58ee779d6eda324d2c0a6ce/transformed/material-1.10.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] /home/<USER>/.gradle/caches/8.9/transforms/2816070cc58ee779d6eda324d2c0a6ce/transformed/material-1.10.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /home/<USER>/.gradle/caches/8.9/transforms/713b6bd2b8c3ac657019a5a1a07d83ab/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /home/<USER>/.gradle/caches/8.9/transforms/713b6bd2b8c3ac657019a5a1a07d83ab/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/dddd54656ef41b816f6060c4df8f6ec3/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/dddd54656ef41b816f6060c4df8f6ec3/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/c2da84da3fff6bb6ca84b344e0d148e9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/c2da84da3fff6bb6ca84b344e0d148e9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:42:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:40:9-35
	android:label
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:38:9-41
	android:fullBackupContent
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:36:9-54
	android:roundIcon
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:39:9-54
	tools:targetApi
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:43:9-29
	android:icon
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:37:9-43
	android:allowBackup
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:34:9-35
	android:theme
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:41:9-55
	android:dataExtractionRules
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:35:9-65
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:33:9-46
activity#com.seres.usb.upgrade.MainActivity
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:46:9-54:20
	android:exported
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:48:13-36
	android:theme
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:49:13-59
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:47:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:50:13-53:29
action#android.intent.action.MAIN
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:51:17-69
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:51:25-66
category#android.intent.category.LAUNCHER
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:52:17-77
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:52:27-74
service#com.seres.usb.upgrade.service.UsbDetectionService
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:57:9-61:56
	android:enabled
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:59:13-35
	android:exported
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:60:13-37
	android:foregroundServiceType
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:61:13-53
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:58:13-56
service#com.seres.usb.upgrade.service.UpgradeTaskService
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:64:9-71:19
	android:enabled
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:66:13-35
	android:exported
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:67:13-36
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:65:13-55
intent-filter#action:name:com.seres.usb.upgrade.action.UPGRADE_SERVICE
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:68:13-70:29
action#com.seres.usb.upgrade.action.UPGRADE_SERVICE
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:69:17-87
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:69:25-84
receiver#com.seres.usb.upgrade.receiver.BootCompleteReceiver
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:74:9-83:20
	android:enabled
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:76:13-35
	android:exported
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:77:13-36
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:75:13-58
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON+category:name:android.intent.category.DEFAULT
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:78:13-82:29
	android:priority
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:78:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:79:17-79
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:79:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:80:17-82
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:80:25-79
category#android.intent.category.DEFAULT
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:81:17-76
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:81:27-73
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:91:13-93:54
	android:resource
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:93:17-51
	android:name
		ADDED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:92:17-67
uses-sdk
INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml
INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.6.0] /home/<USER>/.gradle/caches/8.9/transforms/146863973234875abc30cdcc92af2b0c/transformed/viewbinding-8.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.6.0] /home/<USER>/.gradle/caches/8.9/transforms/146863973234875abc30cdcc92af2b0c/transformed/viewbinding-8.6.0/AndroidManifest.xml:5:5-44
MERGED from [:s2s-sdk-api] /home/<USER>/dds/s2sService/s2s-sdk-api/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:5:5-44
MERGED from [:s2s-sdk-api] /home/<USER>/dds/s2sService/s2s-sdk-api/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.10.0] /home/<USER>/.gradle/caches/8.9/transforms/2816070cc58ee779d6eda324d2c0a6ce/transformed/material-1.10.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] /home/<USER>/.gradle/caches/8.9/transforms/2816070cc58ee779d6eda324d2c0a6ce/transformed/material-1.10.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /home/<USER>/.gradle/caches/8.9/transforms/713b6bd2b8c3ac657019a5a1a07d83ab/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /home/<USER>/.gradle/caches/8.9/transforms/713b6bd2b8c3ac657019a5a1a07d83ab/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /home/<USER>/.gradle/caches/8.9/transforms/83d01cdcfc97a9ee1fa9cc750528eec7/transformed/appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /home/<USER>/.gradle/caches/8.9/transforms/83d01cdcfc97a9ee1fa9cc750528eec7/transformed/appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /home/<USER>/.gradle/caches/8.9/transforms/16178b8cf6033f49fe8192ba1281dfae/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /home/<USER>/.gradle/caches/8.9/transforms/16178b8cf6033f49fe8192ba1281dfae/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /home/<USER>/.gradle/caches/8.9/transforms/e613dbf6633ca10a897becde81915dea/transformed/recyclerview-1.3.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /home/<USER>/.gradle/caches/8.9/transforms/e613dbf6633ca10a897becde81915dea/transformed/recyclerview-1.3.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] /home/<USER>/.gradle/caches/8.9/transforms/83955a3621cb4fdfaa7dc6b5ec8ff01f/transformed/viewpager2-1.1.0-beta02/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] /home/<USER>/.gradle/caches/8.9/transforms/83955a3621cb4fdfaa7dc6b5ec8ff01f/transformed/viewpager2-1.1.0-beta02/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.3.6] /home/<USER>/.gradle/caches/8.9/transforms/cc45b9ac07c9a4e553b7e37f5d5d866f/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] /home/<USER>/.gradle/caches/8.9/transforms/cc45b9ac07c9a4e553b7e37f5d5d866f/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/0d68aef391a1bef8cf69c14377ecc7db/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /home/<USER>/.gradle/caches/8.9/transforms/0d68aef391a1bef8cf69c14377ecc7db/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/853784ca09e9924abb5958855432070b/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/853784ca09e9924abb5958855432070b/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/20f2b4e8c0e7c2394a7da189c0ea435b/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/20f2b4e8c0e7c2394a7da189c0ea435b/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/cc0cf4df6578d734ca157e2604285932/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/cc0cf4df6578d734ca157e2604285932/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/49db75becd20ccde1c6547fe074d871b/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/49db75becd20ccde1c6547fe074d871b/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/29a1cbc1498aa2d2dc598ea4aef7531e/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/29a1cbc1498aa2d2dc598ea4aef7531e/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/c41ae0981c4e37b7f6687f6266df5d19/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/c41ae0981c4e37b7f6687f6266df5d19/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/a793849a76dabef9fb07a79cb2b4eca7/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/a793849a76dabef9fb07a79cb2b4eca7/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/48d3072d62b6e14d8958dffdbfca9eb0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/48d3072d62b6e14d8958dffdbfca9eb0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/f0be03a2227e3e5e9a725862189d13a2/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/f0be03a2227e3e5e9a725862189d13a2/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/df2cf72cc21e0b25a43581f7211b4825/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/df2cf72cc21e0b25a43581f7211b4825/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/36206f3ca5f1efcd5d76add3849a6416/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /home/<USER>/.gradle/caches/8.9/transforms/36206f3ca5f1efcd5d76add3849a6416/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /home/<USER>/.gradle/caches/8.9/transforms/156de66f05358e44cfb2fa9bde8c9f29/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /home/<USER>/.gradle/caches/8.9/transforms/156de66f05358e44cfb2fa9bde8c9f29/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/36e3f2de08975dc5e32f5ba858d74418/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/36e3f2de08975dc5e32f5ba858d74418/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/170ebf14fe6c2628397944d746c89cee/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/170ebf14fe6c2628397944d746c89cee/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/44823380f01d3b61034ea4642f0a606c/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/44823380f01d3b61034ea4642f0a606c/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/79c0fd544c62f74a7a7e75812eb06a1a/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/79c0fd544c62f74a7a7e75812eb06a1a/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/f7f1e67d3a1d990fd27c335eb234d635/transformed/lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/f7f1e67d3a1d990fd27c335eb234d635/transformed/lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/912bdbf86f2221318b1f11a74465cffc/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/912bdbf86f2221318b1f11a74465cffc/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/0e780b3bcd3646138bc1bd95d093899c/transformed/core-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/0e780b3bcd3646138bc1bd95d093899c/transformed/core-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/40e363994e21beada7604a54f43c3c7e/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/40e363994e21beada7604a54f43c3c7e/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/1335f47639b9d8ad8ede95e4cbc029ec/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/1335f47639b9d8ad8ede95e4cbc029ec/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/6bff64edba13d70e91a489be5347be91/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/6bff64edba13d70e91a489be5347be91/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/dddd54656ef41b816f6060c4df8f6ec3/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/dddd54656ef41b816f6060c4df8f6ec3/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/dab1e70fbc520ac05302103961983552/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/dab1e70fbc520ac05302103961983552/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/ac5bc8f2471abf6cbff1c9ebdc2d0848/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/ac5bc8f2471abf6cbff1c9ebdc2d0848/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/c2da84da3fff6bb6ca84b344e0d148e9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/c2da84da3fff6bb6ca84b344e0d148e9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /home/<USER>/.gradle/caches/8.9/transforms/fde77f2acb4a205ab10bd3079ebd36ae/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /home/<USER>/.gradle/caches/8.9/transforms/fde77f2acb4a205ab10bd3079ebd36ae/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/5020891c53660ca6d738d1966fcc7307/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/5020891c53660ca6d738d1966fcc7307/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/6462eb6f120c2c633a0264aa5c519b5b/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/6462eb6f120c2c633a0264aa5c519b5b/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/13a692efac45ce84e3c1717540fe168c/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /home/<USER>/.gradle/caches/8.9/transforms/13a692efac45ce84e3c1717540fe168c/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/dddd54656ef41b816f6060c4df8f6ec3/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /home/<USER>/.gradle/caches/8.9/transforms/dddd54656ef41b816f6060c4df8f6ec3/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
permission#com.seres.usb.upgrade.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
uses-permission#com.seres.usb.upgrade.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
