  Manifest android  R android  
permission android.Manifest  MANAGE_EXTERNAL_STORAGE android.Manifest.permission  READ_EXTERNAL_STORAGE android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  color 	android.R  holo_green_dark android.R.color  holo_green_light android.R.color  holo_orange_dark android.R.color  holo_orange_light android.R.color  
holo_red_dark android.R.color  holo_red_light android.R.color  Application android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  Service android.app  ActivityMainBinding android.app.Activity  ActivityResultContracts android.app.Activity  Bundle android.app.Activity  
ContextCompat android.app.Activity  LinearLayoutManager android.app.Activity  LogUtils android.app.Activity  Manifest android.app.Activity  PackageManager android.app.Activity  StorageDeviceAdapter android.app.Activity  StorageStatusManager android.app.Activity  TAG android.app.Activity  Toast android.app.Activity  all android.app.Activity  apply android.app.Activity  arrayOf android.app.Activity  checkPermissions android.app.Activity  filter android.app.Activity  initStorageManager android.app.Activity  
isInitialized android.app.Activity  
isNotEmpty android.app.Activity  onCreate android.app.Activity  onResume android.app.Activity  refreshStorageDevices android.app.Activity  registerForActivityResult android.app.Activity  setContentView android.app.Activity  setupUI android.app.Activity  storageDeviceAdapter android.app.Activity  storageStatusManager android.app.Activity  toTypedArray android.app.Activity  	Exception android.app.Application  Intent android.app.Application  LogUtils android.app.Application  TAG android.app.Application  UsbDetectionService android.app.Application  java android.app.Application  onCreate android.app.Application  startForegroundService android.app.Application  startUsbDetectionService android.app.Application  apply android.app.NotificationChannel  description android.app.NotificationChannel  getAPPLY android.app.NotificationChannel  getApply android.app.NotificationChannel  getDESCRIPTION android.app.NotificationChannel  getDescription android.app.NotificationChannel  setDescription android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  notify android.app.NotificationManager  ACTION_STORAGE_STATUS_CHANGED android.app.Service  Binder android.app.Service  Boolean android.app.Service  BroadcastReceiver android.app.Service  
CHANNEL_ID android.app.Service  Context android.app.Service  EXTRA_STORAGE_DEVICES android.app.Service  EXTRA_USB_CONNECTED android.app.Service  EXTRA_USB_PATH android.app.Service  	Exception android.app.Service  	Executors android.app.Service  File android.app.Service  IBinder android.app.Service  Int android.app.Service  Intent android.app.Service  IntentFilter android.app.Service  List android.app.Service  LogUtils android.app.Service  NOTIFICATION_ID android.app.Service  Notification android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  R android.app.Service  START_STICKY android.app.Service  StorageManager android.app.Service  StorageStatusManager android.app.Service  
StorageVolume android.app.Service  String android.app.Service  TAG android.app.Service  UpgradePackageAnalyzer android.app.Service  UpgradeTaskInfo android.app.Service  UpgradeTaskManager android.app.Service  UpgradeTaskService android.app.Service  apply android.app.Service  broadcastStorageStatus android.app.Service  checkExistingUsbDevices android.app.Service  createNotification android.app.Service  createNotificationChannel android.app.Service  	emptyList android.app.Service  getSystemService android.app.Service  
getVolumePath android.app.Service  handleUsbMounted android.app.Service  handleUsbUnmounted android.app.Service  
isNotEmpty android.app.Service  	javaClass android.app.Service  let android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  onUnbind android.app.Service  registerReceiver android.app.Service  registerUsbReceiver android.app.Service  
sendBroadcast android.app.Service  setupStorageStatusListener android.app.Service  startForeground android.app.Service  unregisterReceiver android.app.Service  updateNotification android.app.Service  BroadcastReceiver android.content  
ComponentName android.content  Context android.content  Intent android.content  IntentFilter android.content  ServiceConnection android.content  Context !android.content.BroadcastReceiver  	Exception !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  LogUtils !android.content.BroadcastReceiver  TAG !android.content.BroadcastReceiver  UsbDetectionService !android.content.BroadcastReceiver  handleUsbMounted !android.content.BroadcastReceiver  handleUsbUnmounted !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  let !android.content.BroadcastReceiver  equals android.content.ComponentName  ACTION_STORAGE_STATUS_CHANGED android.content.Context  ActivityMainBinding android.content.Context  ActivityResultContracts android.content.Context  BIND_AUTO_CREATE android.content.Context  Binder android.content.Context  Boolean android.content.Context  BroadcastReceiver android.content.Context  Bundle android.content.Context  
CHANNEL_ID android.content.Context  Context android.content.Context  
ContextCompat android.content.Context  EXTRA_STORAGE_DEVICES android.content.Context  EXTRA_USB_CONNECTED android.content.Context  EXTRA_USB_PATH android.content.Context  	Exception android.content.Context  	Executors android.content.Context  File android.content.Context  IBinder android.content.Context  Int android.content.Context  Intent android.content.Context  IntentFilter android.content.Context  LinearLayoutManager android.content.Context  List android.content.Context  LogUtils android.content.Context  Manifest android.content.Context  NOTIFICATION_ID android.content.Context  NOTIFICATION_SERVICE android.content.Context  Notification android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  PackageManager android.content.Context  R android.content.Context  START_STICKY android.content.Context  STORAGE_SERVICE android.content.Context  StorageDeviceAdapter android.content.Context  StorageManager android.content.Context  StorageStatusManager android.content.Context  
StorageVolume android.content.Context  String android.content.Context  TAG android.content.Context  Toast android.content.Context  UpgradePackageAnalyzer android.content.Context  UpgradeTaskInfo android.content.Context  UpgradeTaskManager android.content.Context  UpgradeTaskService android.content.Context  UsbDetectionService android.content.Context  all android.content.Context  applicationContext android.content.Context  apply android.content.Context  arrayOf android.content.Context  bindService android.content.Context  broadcastStorageStatus android.content.Context  checkExistingUsbDevices android.content.Context  checkPermissions android.content.Context  createNotification android.content.Context  createNotificationChannel android.content.Context  	emptyList android.content.Context  filter android.content.Context  getAPPLICATIONContext android.content.Context  getApplicationContext android.content.Context  getColor android.content.Context  getColorStateList android.content.Context  getExternalFilesDir android.content.Context  getPACKAGEManager android.content.Context  getPACKAGEName android.content.Context  getPackageManager android.content.Context  getPackageName android.content.Context  getSystemService android.content.Context  
getVolumePath android.content.Context  handleUsbMounted android.content.Context  handleUsbUnmounted android.content.Context  initStorageManager android.content.Context  
isInitialized android.content.Context  
isNotEmpty android.content.Context  java android.content.Context  	javaClass android.content.Context  let android.content.Context  onCreate android.content.Context  	onDestroy android.content.Context  onResume android.content.Context  onUnbind android.content.Context  packageManager android.content.Context  packageName android.content.Context  refreshStorageDevices android.content.Context  registerForActivityResult android.content.Context  registerReceiver android.content.Context  registerUsbReceiver android.content.Context  
sendBroadcast android.content.Context  setApplicationContext android.content.Context  setContentView android.content.Context  setPackageManager android.content.Context  setPackageName android.content.Context  setupStorageStatusListener android.content.Context  setupUI android.content.Context  
startActivity android.content.Context  startForeground android.content.Context  startForegroundService android.content.Context  startUsbDetectionService android.content.Context  storageDeviceAdapter android.content.Context  storageStatusManager android.content.Context  toTypedArray android.content.Context  
unbindService android.content.Context  unregisterReceiver android.content.Context  updateNotification android.content.Context  ACTION_STORAGE_STATUS_CHANGED android.content.ContextWrapper  ActivityMainBinding android.content.ContextWrapper  ActivityResultContracts android.content.ContextWrapper  Binder android.content.ContextWrapper  Boolean android.content.ContextWrapper  BroadcastReceiver android.content.ContextWrapper  Bundle android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  Context android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  EXTRA_STORAGE_DEVICES android.content.ContextWrapper  EXTRA_USB_CONNECTED android.content.ContextWrapper  EXTRA_USB_PATH android.content.ContextWrapper  	Exception android.content.ContextWrapper  	Executors android.content.ContextWrapper  File android.content.ContextWrapper  IBinder android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  IntentFilter android.content.ContextWrapper  LinearLayoutManager android.content.ContextWrapper  List android.content.ContextWrapper  LogUtils android.content.ContextWrapper  Manifest android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  Notification android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  PackageManager android.content.ContextWrapper  R android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  StorageDeviceAdapter android.content.ContextWrapper  StorageManager android.content.ContextWrapper  StorageStatusManager android.content.ContextWrapper  
StorageVolume android.content.ContextWrapper  String android.content.ContextWrapper  TAG android.content.ContextWrapper  Toast android.content.ContextWrapper  UpgradePackageAnalyzer android.content.ContextWrapper  UpgradeTaskInfo android.content.ContextWrapper  UpgradeTaskManager android.content.ContextWrapper  UpgradeTaskService android.content.ContextWrapper  UsbDetectionService android.content.ContextWrapper  all android.content.ContextWrapper  apply android.content.ContextWrapper  arrayOf android.content.ContextWrapper  broadcastStorageStatus android.content.ContextWrapper  checkExistingUsbDevices android.content.ContextWrapper  checkPermissions android.content.ContextWrapper  createNotification android.content.ContextWrapper  createNotificationChannel android.content.ContextWrapper  	emptyList android.content.ContextWrapper  filter android.content.ContextWrapper  getSystemService android.content.ContextWrapper  
getVolumePath android.content.ContextWrapper  handleUsbMounted android.content.ContextWrapper  handleUsbUnmounted android.content.ContextWrapper  initStorageManager android.content.ContextWrapper  
isInitialized android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  java android.content.ContextWrapper  	javaClass android.content.ContextWrapper  let android.content.ContextWrapper  onCreate android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  onResume android.content.ContextWrapper  onUnbind android.content.ContextWrapper  refreshStorageDevices android.content.ContextWrapper  registerForActivityResult android.content.ContextWrapper  registerReceiver android.content.ContextWrapper  registerUsbReceiver android.content.ContextWrapper  
sendBroadcast android.content.ContextWrapper  setContentView android.content.ContextWrapper  setupStorageStatusListener android.content.ContextWrapper  setupUI android.content.ContextWrapper  startForeground android.content.ContextWrapper  startForegroundService android.content.ContextWrapper  startUsbDetectionService android.content.ContextWrapper  storageDeviceAdapter android.content.ContextWrapper  storageStatusManager android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  unregisterReceiver android.content.ContextWrapper  updateNotification android.content.ContextWrapper  ACTION_BOOT_COMPLETED android.content.Intent  ACTION_MEDIA_EJECT android.content.Intent  ACTION_MEDIA_MOUNTED android.content.Intent  ACTION_MEDIA_REMOVED android.content.Intent  ACTION_MEDIA_UNMOUNTED android.content.Intent  ACTION_OPEN_DOCUMENT_TREE android.content.Intent  ACTION_VIEW android.content.Intent  EXTRA_STORAGE_DEVICES android.content.Intent  EXTRA_USB_CONNECTED android.content.Intent  EXTRA_USB_PATH android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  Intent android.content.Intent  S2S_SERVICE_PACKAGE android.content.Intent  Uri android.content.Intent  action android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  data android.content.Intent  	getACTION android.content.Intent  getAPPLY android.content.Intent  	getAction android.content.Intent  getApply android.content.Intent  getDATA android.content.Intent  getData android.content.Intent  getGETUriForFile android.content.Intent  getGetUriForFile android.content.Intent  
getUriForFile android.content.Intent  putExtra android.content.Intent  resolveActivity android.content.Intent  	setAction android.content.Intent  setData android.content.Intent  setDataAndType android.content.Intent  
setPackage android.content.Intent  Intent android.content.IntentFilter  	addAction android.content.IntentFilter  
addDataScheme android.content.IntentFilter  apply android.content.IntentFilter  getAPPLY android.content.IntentFilter  getApply android.content.IntentFilter  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  ColorStateList android.content.res  Uri android.net  fromFile android.net.Uri  getPATH android.net.Uri  getPath android.net.Uri  parse android.net.Uri  path android.net.Uri  setPath android.net.Uri  Binder 
android.os  Bundle 
android.os  Environment 
android.os  IBinder 
android.os  Parcel 
android.os  
Parcelable 
android.os  apply android.os.BaseBundle  
getBoolean android.os.BaseBundle  	getString android.os.BaseBundle  putLong android.os.BaseBundle  	putString android.os.BaseBundle  Bundle android.os.Binder  LogUtils android.os.Binder  TAG android.os.Binder  UpgradeTaskService android.os.Binder  System android.os.Bundle  apply android.os.Bundle  getAPPLY android.os.Bundle  getApply android.os.Bundle  
getBoolean android.os.Bundle  	getString android.os.Bundle  putLong android.os.Bundle  	putString android.os.Bundle  getDataDirectory android.os.Environment  createTypedArrayList android.os.Parcel  readLong android.os.Parcel  
readString android.os.Parcel  	writeLong android.os.Parcel  writeString android.os.Parcel  writeTypedList android.os.Parcel  Creator android.os.Parcelable  StorageManager android.os.storage  
StorageVolume android.os.storage  getSTORAGEVolumes !android.os.storage.StorageManager  getStorageVolumes !android.os.storage.StorageManager  setStorageVolumes !android.os.storage.StorageManager  storageVolumes !android.os.storage.StorageManager  getDescription  android.os.storage.StorageVolume  getISPrimary  android.os.storage.StorageVolume  getISRemovable  android.os.storage.StorageVolume  getIsPrimary  android.os.storage.StorageVolume  getIsRemovable  android.os.storage.StorageVolume  getJAVAClass  android.os.storage.StorageVolume  getJavaClass  android.os.storage.StorageVolume  getSTATE  android.os.storage.StorageVolume  getState  android.os.storage.StorageVolume  	isPrimary  android.os.storage.StorageVolume  isRemovable  android.os.storage.StorageVolume  	javaClass  android.os.storage.StorageVolume  
setPrimary  android.os.storage.StorageVolume  setRemovable  android.os.storage.StorageVolume  setState  android.os.storage.StorageVolume  state  android.os.storage.StorageVolume  Log android.util  d android.util.Log  e android.util.Log  getStackTraceString android.util.Log  i android.util.Log  w android.util.Log  LayoutInflater android.view  View android.view  	ViewGroup android.view  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityResultContracts  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  LinearLayoutManager  android.view.ContextThemeWrapper  LogUtils  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  StorageDeviceAdapter  android.view.ContextThemeWrapper  StorageStatusManager  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  all  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  arrayOf  android.view.ContextThemeWrapper  checkPermissions  android.view.ContextThemeWrapper  filter  android.view.ContextThemeWrapper  initStorageManager  android.view.ContextThemeWrapper  
isInitialized  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  onResume  android.view.ContextThemeWrapper  refreshStorageDevices  android.view.ContextThemeWrapper  registerForActivityResult  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  setupUI  android.view.ContextThemeWrapper  storageDeviceAdapter  android.view.ContextThemeWrapper  storageStatusManager  android.view.ContextThemeWrapper  toTypedArray  android.view.ContextThemeWrapper  from android.view.LayoutInflater  inflate android.view.LayoutInflater  apply android.view.View  findViewById android.view.View  setOnClickListener android.view.View  setTextColor android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  apply android.view.ViewGroup  context android.view.ViewGroup  
getCONTEXT android.view.ViewGroup  
getContext android.view.ViewGroup  
setContext android.view.ViewGroup  Button android.widget  ProgressBar android.widget  
ScrollView android.widget  TextView android.widget  Toast android.widget  getISEnabled android.widget.Button  getIsEnabled android.widget.Button  getTEXT android.widget.Button  getText android.widget.Button  	isEnabled android.widget.Button  
setEnabled android.widget.Button  setOnClickListener android.widget.Button  setText android.widget.Button  text android.widget.Button  getPROGRESS android.widget.ProgressBar  getPROGRESSTintList android.widget.ProgressBar  getProgress android.widget.ProgressBar  getProgressTintList android.widget.ProgressBar  progress android.widget.ProgressBar  progressTintList android.widget.ProgressBar  setProgress android.widget.ProgressBar  setProgressTintList android.widget.ProgressBar  getTEXT android.widget.TextView  getText android.widget.TextView  setOnClickListener android.widget.TextView  setText android.widget.TextView  setTextColor android.widget.TextView  text android.widget.TextView  LENGTH_LONG android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityResultContracts #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  LinearLayoutManager #androidx.activity.ComponentActivity  LogUtils #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  StorageDeviceAdapter #androidx.activity.ComponentActivity  StorageStatusManager #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  all #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  arrayOf #androidx.activity.ComponentActivity  checkPermissions #androidx.activity.ComponentActivity  filter #androidx.activity.ComponentActivity  initStorageManager #androidx.activity.ComponentActivity  
isInitialized #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  onResume #androidx.activity.ComponentActivity  refreshStorageDevices #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  setupUI #androidx.activity.ComponentActivity  storageDeviceAdapter #androidx.activity.ComponentActivity  storageStatusManager #androidx.activity.ComponentActivity  toTypedArray #androidx.activity.ComponentActivity  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  invoke ^androidx.activity.result.contract.ActivityResultContracts.RequestMultiplePermissions.Companion  AppCompatActivity androidx.appcompat.app  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityResultContracts (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  
ContextCompat (androidx.appcompat.app.AppCompatActivity  LinearLayoutManager (androidx.appcompat.app.AppCompatActivity  LogUtils (androidx.appcompat.app.AppCompatActivity  Manifest (androidx.appcompat.app.AppCompatActivity  PackageManager (androidx.appcompat.app.AppCompatActivity  StorageDeviceAdapter (androidx.appcompat.app.AppCompatActivity  StorageStatusManager (androidx.appcompat.app.AppCompatActivity  TAG (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  all (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  arrayOf (androidx.appcompat.app.AppCompatActivity  checkPermissions (androidx.appcompat.app.AppCompatActivity  filter (androidx.appcompat.app.AppCompatActivity  initStorageManager (androidx.appcompat.app.AppCompatActivity  
isInitialized (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  refreshStorageDevices (androidx.appcompat.app.AppCompatActivity  registerForActivityResult (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  setupUI (androidx.appcompat.app.AppCompatActivity  storageDeviceAdapter (androidx.appcompat.app.AppCompatActivity  storageStatusManager (androidx.appcompat.app.AppCompatActivity  toTypedArray (androidx.appcompat.app.AppCompatActivity  NotificationCompat androidx.core.app  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityResultContracts #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  LinearLayoutManager #androidx.core.app.ComponentActivity  LogUtils #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  StorageDeviceAdapter #androidx.core.app.ComponentActivity  StorageStatusManager #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  all #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  arrayOf #androidx.core.app.ComponentActivity  checkPermissions #androidx.core.app.ComponentActivity  filter #androidx.core.app.ComponentActivity  initStorageManager #androidx.core.app.ComponentActivity  
isInitialized #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  onResume #androidx.core.app.ComponentActivity  refreshStorageDevices #androidx.core.app.ComponentActivity  registerForActivityResult #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  setupUI #androidx.core.app.ComponentActivity  storageDeviceAdapter #androidx.core.app.ComponentActivity  storageStatusManager #androidx.core.app.ComponentActivity  toTypedArray #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
ContextCompat androidx.core.content  FileProvider androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  
getUriForFile "androidx.core.content.FileProvider  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityResultContracts &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  
ContextCompat &androidx.fragment.app.FragmentActivity  LinearLayoutManager &androidx.fragment.app.FragmentActivity  LogUtils &androidx.fragment.app.FragmentActivity  Manifest &androidx.fragment.app.FragmentActivity  PackageManager &androidx.fragment.app.FragmentActivity  StorageDeviceAdapter &androidx.fragment.app.FragmentActivity  StorageStatusManager &androidx.fragment.app.FragmentActivity  TAG &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  all &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  arrayOf &androidx.fragment.app.FragmentActivity  checkPermissions &androidx.fragment.app.FragmentActivity  filter &androidx.fragment.app.FragmentActivity  initStorageManager &androidx.fragment.app.FragmentActivity  
isInitialized &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  refreshStorageDevices &androidx.fragment.app.FragmentActivity  registerForActivityResult &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  setupUI &androidx.fragment.app.FragmentActivity  storageDeviceAdapter &androidx.fragment.app.FragmentActivity  storageStatusManager &androidx.fragment.app.FragmentActivity  toTypedArray &androidx.fragment.app.FragmentActivity  LinearLayoutManager androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
LayoutManager )androidx.recyclerview.widget.RecyclerView  LinearLayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  apply )androidx.recyclerview.widget.RecyclerView  
getADAPTER )androidx.recyclerview.widget.RecyclerView  getAPPLY )androidx.recyclerview.widget.RecyclerView  
getAdapter )androidx.recyclerview.widget.RecyclerView  getApply )androidx.recyclerview.widget.RecyclerView  getLAYOUTManager )androidx.recyclerview.widget.RecyclerView  getLayoutManager )androidx.recyclerview.widget.RecyclerView  getSTORAGEDeviceAdapter )androidx.recyclerview.widget.RecyclerView  getStorageDeviceAdapter )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  
setAdapter )androidx.recyclerview.widget.RecyclerView  setLayoutManager )androidx.recyclerview.widget.RecyclerView  storageDeviceAdapter )androidx.recyclerview.widget.RecyclerView  Button 1androidx.recyclerview.widget.RecyclerView.Adapter  Context 1androidx.recyclerview.widget.RecyclerView.Adapter  	Exception 1androidx.recyclerview.widget.RecyclerView.Adapter  File 1androidx.recyclerview.widget.RecyclerView.Adapter  FileProvider 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  Intent 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  List 1androidx.recyclerview.widget.RecyclerView.Adapter  LogUtils 1androidx.recyclerview.widget.RecyclerView.Adapter  ProgressBar 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  StorageDeviceViewHolder 1androidx.recyclerview.widget.RecyclerView.Adapter  StorageStatusManager 1androidx.recyclerview.widget.RecyclerView.Adapter  TextView 1androidx.recyclerview.widget.RecyclerView.Adapter  Uri 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  android 1androidx.recyclerview.widget.RecyclerView.Adapter  apply 1androidx.recyclerview.widget.RecyclerView.Adapter  	emptyList 1androidx.recyclerview.widget.RecyclerView.Adapter  
getUriForFile 1androidx.recyclerview.widget.RecyclerView.Adapter  notifyDataSetChanged 1androidx.recyclerview.widget.RecyclerView.Adapter  openStorageDevice 1androidx.recyclerview.widget.RecyclerView.Adapter  openWithAlternativeMethod 1androidx.recyclerview.widget.RecyclerView.Adapter  openWithDocumentPicker 1androidx.recyclerview.widget.RecyclerView.Adapter  
updateDevices 1androidx.recyclerview.widget.RecyclerView.Adapter  Button 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ProgressBar 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  TextView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  View 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Gson com.google.gson  JsonSyntaxException com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  message #com.google.gson.JsonSyntaxException  ActivityMainBinding com.seres.usb.upgrade  ActivityResultContracts com.seres.usb.upgrade  
ContextCompat com.seres.usb.upgrade  	Exception com.seres.usb.upgrade  Intent com.seres.usb.upgrade  LinearLayoutManager com.seres.usb.upgrade  LogUtils com.seres.usb.upgrade  MainActivity com.seres.usb.upgrade  Manifest com.seres.usb.upgrade  PackageManager com.seres.usb.upgrade  R com.seres.usb.upgrade  StorageDeviceAdapter com.seres.usb.upgrade  StorageStatusManager com.seres.usb.upgrade  TAG com.seres.usb.upgrade  Toast com.seres.usb.upgrade  UsbDetectionService com.seres.usb.upgrade  UsbUpgradeApplication com.seres.usb.upgrade  all com.seres.usb.upgrade  apply com.seres.usb.upgrade  arrayOf com.seres.usb.upgrade  filter com.seres.usb.upgrade  
isInitialized com.seres.usb.upgrade  
isNotEmpty com.seres.usb.upgrade  java com.seres.usb.upgrade  storageDeviceAdapter com.seres.usb.upgrade  toTypedArray com.seres.usb.upgrade  ActivityMainBinding "com.seres.usb.upgrade.MainActivity  ActivityResultContracts "com.seres.usb.upgrade.MainActivity  Bundle "com.seres.usb.upgrade.MainActivity  
ContextCompat "com.seres.usb.upgrade.MainActivity  LinearLayoutManager "com.seres.usb.upgrade.MainActivity  LogUtils "com.seres.usb.upgrade.MainActivity  Manifest "com.seres.usb.upgrade.MainActivity  PackageManager "com.seres.usb.upgrade.MainActivity  StorageDeviceAdapter "com.seres.usb.upgrade.MainActivity  StorageStatusManager "com.seres.usb.upgrade.MainActivity  TAG "com.seres.usb.upgrade.MainActivity  Toast "com.seres.usb.upgrade.MainActivity  all "com.seres.usb.upgrade.MainActivity  apply "com.seres.usb.upgrade.MainActivity  arrayOf "com.seres.usb.upgrade.MainActivity  binding "com.seres.usb.upgrade.MainActivity  checkPermissions "com.seres.usb.upgrade.MainActivity  filter "com.seres.usb.upgrade.MainActivity  getALL "com.seres.usb.upgrade.MainActivity  getAPPLY "com.seres.usb.upgrade.MainActivity  
getARRAYOf "com.seres.usb.upgrade.MainActivity  getAll "com.seres.usb.upgrade.MainActivity  getApply "com.seres.usb.upgrade.MainActivity  
getArrayOf "com.seres.usb.upgrade.MainActivity  	getFILTER "com.seres.usb.upgrade.MainActivity  	getFilter "com.seres.usb.upgrade.MainActivity  
getISNotEmpty "com.seres.usb.upgrade.MainActivity  
getIsNotEmpty "com.seres.usb.upgrade.MainActivity  getLAYOUTInflater "com.seres.usb.upgrade.MainActivity  getLayoutInflater "com.seres.usb.upgrade.MainActivity  getTOTypedArray "com.seres.usb.upgrade.MainActivity  getToTypedArray "com.seres.usb.upgrade.MainActivity  initStorageManager "com.seres.usb.upgrade.MainActivity  
isInitialized "com.seres.usb.upgrade.MainActivity  
isNotEmpty "com.seres.usb.upgrade.MainActivity  layoutInflater "com.seres.usb.upgrade.MainActivity  permissionLauncher "com.seres.usb.upgrade.MainActivity  refreshStorageDevices "com.seres.usb.upgrade.MainActivity  registerForActivityResult "com.seres.usb.upgrade.MainActivity  setContentView "com.seres.usb.upgrade.MainActivity  setLayoutInflater "com.seres.usb.upgrade.MainActivity  setupUI "com.seres.usb.upgrade.MainActivity  storageDeviceAdapter "com.seres.usb.upgrade.MainActivity  storageStatusManager "com.seres.usb.upgrade.MainActivity  toTypedArray "com.seres.usb.upgrade.MainActivity  ActivityMainBinding ,com.seres.usb.upgrade.MainActivity.Companion  ActivityResultContracts ,com.seres.usb.upgrade.MainActivity.Companion  Bundle ,com.seres.usb.upgrade.MainActivity.Companion  
ContextCompat ,com.seres.usb.upgrade.MainActivity.Companion  LinearLayoutManager ,com.seres.usb.upgrade.MainActivity.Companion  LogUtils ,com.seres.usb.upgrade.MainActivity.Companion  Manifest ,com.seres.usb.upgrade.MainActivity.Companion  PackageManager ,com.seres.usb.upgrade.MainActivity.Companion  StorageDeviceAdapter ,com.seres.usb.upgrade.MainActivity.Companion  StorageStatusManager ,com.seres.usb.upgrade.MainActivity.Companion  TAG ,com.seres.usb.upgrade.MainActivity.Companion  Toast ,com.seres.usb.upgrade.MainActivity.Companion  all ,com.seres.usb.upgrade.MainActivity.Companion  apply ,com.seres.usb.upgrade.MainActivity.Companion  arrayOf ,com.seres.usb.upgrade.MainActivity.Companion  filter ,com.seres.usb.upgrade.MainActivity.Companion  getALL ,com.seres.usb.upgrade.MainActivity.Companion  getAPPLY ,com.seres.usb.upgrade.MainActivity.Companion  
getARRAYOf ,com.seres.usb.upgrade.MainActivity.Companion  getAll ,com.seres.usb.upgrade.MainActivity.Companion  getApply ,com.seres.usb.upgrade.MainActivity.Companion  
getArrayOf ,com.seres.usb.upgrade.MainActivity.Companion  	getFILTER ,com.seres.usb.upgrade.MainActivity.Companion  	getFilter ,com.seres.usb.upgrade.MainActivity.Companion  
getISNotEmpty ,com.seres.usb.upgrade.MainActivity.Companion  
getIsNotEmpty ,com.seres.usb.upgrade.MainActivity.Companion  getTOTypedArray ,com.seres.usb.upgrade.MainActivity.Companion  getToTypedArray ,com.seres.usb.upgrade.MainActivity.Companion  
isInitialized ,com.seres.usb.upgrade.MainActivity.Companion  
isNotEmpty ,com.seres.usb.upgrade.MainActivity.Companion  storageDeviceAdapter ,com.seres.usb.upgrade.MainActivity.Companion  toTypedArray ,com.seres.usb.upgrade.MainActivity.Companion  drawable com.seres.usb.upgrade.R  id com.seres.usb.upgrade.R  layout com.seres.usb.upgrade.R  ic_usb  com.seres.usb.upgrade.R.drawable  
btnOpenDevice com.seres.usb.upgrade.R.id  progressBarUsage com.seres.usb.upgrade.R.id  tvDeviceName com.seres.usb.upgrade.R.id  tvDevicePath com.seres.usb.upgrade.R.id  tvDeviceStatus com.seres.usb.upgrade.R.id  
tvStorageInfo com.seres.usb.upgrade.R.id  tvUsagePercentage com.seres.usb.upgrade.R.id  item_storage_device com.seres.usb.upgrade.R.layout  	Exception +com.seres.usb.upgrade.UsbUpgradeApplication  Intent +com.seres.usb.upgrade.UsbUpgradeApplication  LogUtils +com.seres.usb.upgrade.UsbUpgradeApplication  TAG +com.seres.usb.upgrade.UsbUpgradeApplication  UsbDetectionService +com.seres.usb.upgrade.UsbUpgradeApplication  java +com.seres.usb.upgrade.UsbUpgradeApplication  startForegroundService +com.seres.usb.upgrade.UsbUpgradeApplication  startUsbDetectionService +com.seres.usb.upgrade.UsbUpgradeApplication  	Exception 5com.seres.usb.upgrade.UsbUpgradeApplication.Companion  Intent 5com.seres.usb.upgrade.UsbUpgradeApplication.Companion  LogUtils 5com.seres.usb.upgrade.UsbUpgradeApplication.Companion  TAG 5com.seres.usb.upgrade.UsbUpgradeApplication.Companion  UsbDetectionService 5com.seres.usb.upgrade.UsbUpgradeApplication.Companion  java 5com.seres.usb.upgrade.UsbUpgradeApplication.Companion  	Exception com.seres.usb.upgrade.adapter  File com.seres.usb.upgrade.adapter  FileProvider com.seres.usb.upgrade.adapter  Int com.seres.usb.upgrade.adapter  Intent com.seres.usb.upgrade.adapter  LayoutInflater com.seres.usb.upgrade.adapter  List com.seres.usb.upgrade.adapter  LogUtils com.seres.usb.upgrade.adapter  R com.seres.usb.upgrade.adapter  StorageDeviceAdapter com.seres.usb.upgrade.adapter  StorageDeviceViewHolder com.seres.usb.upgrade.adapter  StorageStatusManager com.seres.usb.upgrade.adapter  Uri com.seres.usb.upgrade.adapter  android com.seres.usb.upgrade.adapter  apply com.seres.usb.upgrade.adapter  	emptyList com.seres.usb.upgrade.adapter  
getUriForFile com.seres.usb.upgrade.adapter  Button 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  Context 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  	Exception 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  File 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  FileProvider 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  Int 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  Intent 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  LayoutInflater 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  List 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  LogUtils 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  ProgressBar 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  R 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  RecyclerView 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  StorageDeviceViewHolder 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  StorageStatusManager 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  TAG 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  TextView 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  Uri 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  View 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  	ViewGroup 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  android 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  apply 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  context 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  devices 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  	emptyList 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  
getANDROID 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  getAPPLY 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  
getAndroid 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  getApply 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  
getUriForFile 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  notifyDataSetChanged 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  openStorageDevice 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  openWithAlternativeMethod 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  openWithDocumentPicker 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  
updateDevices 2com.seres.usb.upgrade.adapter.StorageDeviceAdapter  Button Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  ProgressBar Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  R Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  TextView Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  View Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  
btnOpenDevice Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  progressBarUsage Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  tvDeviceName Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  tvDevicePath Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  tvDeviceStatus Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  
tvStorageInfo Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  tvUsagePercentage Jcom.seres.usb.upgrade.adapter.StorageDeviceAdapter.StorageDeviceViewHolder  IAsyncResultCallback com.seres.usb.upgrade.aidl  IS2SService com.seres.usb.upgrade.aidl  Stub /com.seres.usb.upgrade.aidl.IAsyncResultCallback  Bundle 4com.seres.usb.upgrade.aidl.IAsyncResultCallback.Stub  LogUtils 4com.seres.usb.upgrade.aidl.IAsyncResultCallback.Stub  TAG 4com.seres.usb.upgrade.aidl.IAsyncResultCallback.Stub  Stub &com.seres.usb.upgrade.aidl.IS2SService  invokeAsync &com.seres.usb.upgrade.aidl.IS2SService  asInterface +com.seres.usb.upgrade.aidl.IS2SService.Stub  Boolean com.seres.usb.upgrade.analyzer  	ByteArray com.seres.usb.upgrade.analyzer  	Exception com.seres.usb.upgrade.analyzer  File com.seres.usb.upgrade.analyzer  FileInputStream com.seres.usb.upgrade.analyzer  Gson com.seres.usb.upgrade.analyzer  Int com.seres.usb.upgrade.analyzer  List com.seres.usb.upgrade.analyzer  LogUtils com.seres.usb.upgrade.analyzer  
MessageDigest com.seres.usb.upgrade.analyzer  
PackageConfig com.seres.usb.upgrade.analyzer  Regex com.seres.usb.upgrade.analyzer  String com.seres.usb.upgrade.analyzer  System com.seres.usb.upgrade.analyzer  UUID com.seres.usb.upgrade.analyzer  
UpgradeConfig com.seres.usb.upgrade.analyzer  UpgradePackageAnalyzer com.seres.usb.upgrade.analyzer  UpgradePackageInfo com.seres.usb.upgrade.analyzer  UpgradeTaskInfo com.seres.usb.upgrade.analyzer  UpgradeTaskStatus com.seres.usb.upgrade.analyzer  also com.seres.usb.upgrade.analyzer  contains com.seres.usb.upgrade.analyzer  	emptyList com.seres.usb.upgrade.analyzer  	extension com.seres.usb.upgrade.analyzer  find com.seres.usb.upgrade.analyzer  forEach com.seres.usb.upgrade.analyzer  format com.seres.usb.upgrade.analyzer  invoke com.seres.usb.upgrade.analyzer  java com.seres.usb.upgrade.analyzer  joinToString com.seres.usb.upgrade.analyzer  	lowercase com.seres.usb.upgrade.analyzer  
mutableListOf com.seres.usb.upgrade.analyzer  readText com.seres.usb.upgrade.analyzer  setOf com.seres.usb.upgrade.analyzer  	substring com.seres.usb.upgrade.analyzer  sumOf com.seres.usb.upgrade.analyzer  
toMutableList com.seres.usb.upgrade.analyzer  use com.seres.usb.upgrade.analyzer  Boolean ,com.seres.usb.upgrade.analyzer.PackageConfig  String ,com.seres.usb.upgrade.analyzer.PackageConfig  fileName ,com.seres.usb.upgrade.analyzer.PackageConfig  name ,com.seres.usb.upgrade.analyzer.PackageConfig  	targetEcu ,com.seres.usb.upgrade.analyzer.PackageConfig  type ,com.seres.usb.upgrade.analyzer.PackageConfig  version ,com.seres.usb.upgrade.analyzer.PackageConfig  List ,com.seres.usb.upgrade.analyzer.UpgradeConfig  
PackageConfig ,com.seres.usb.upgrade.analyzer.UpgradeConfig  String ,com.seres.usb.upgrade.analyzer.UpgradeConfig  description ,com.seres.usb.upgrade.analyzer.UpgradeConfig  	emptyList ,com.seres.usb.upgrade.analyzer.UpgradeConfig  packages ,com.seres.usb.upgrade.analyzer.UpgradeConfig  taskName ,com.seres.usb.upgrade.analyzer.UpgradeConfig  version ,com.seres.usb.upgrade.analyzer.UpgradeConfig  Boolean 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  	ByteArray 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  	Exception 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  File 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  FileInputStream 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  Gson 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  Int 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  JsonSyntaxException 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  List 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  LogUtils 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  
MessageDigest 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  Regex 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  String 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  System 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  TAG 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  UUID 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  
UpgradeConfig 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  UpgradePackageInfo 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  UpgradeTaskInfo 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  UpgradeTaskStatus 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  also 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  analyzePackageFile 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  analyzeUpgradePackages 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  calculateChecksum 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  configFileName 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  contains 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  	extension 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  extractVersionFromFileName 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  find 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  forEach 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  format 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  generateTaskId 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getALSO 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getAlso 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getCONTAINS 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getContains 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getFIND 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  
getFOREach 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  	getFORMAT 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getFind 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  
getForEach 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  	getFormat 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getJOINToString 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getJoinToString 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getLOWERCASE 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getLowercase 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getMUTABLEListOf 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getMutableListOf 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getREADText 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getReadText 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getSETOf 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getSUBSTRING 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getSUMOf 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getSetOf 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getSubstring 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getSumOf 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getTOMutableList 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  
getTargetPath 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getToMutableList 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getUSE 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  getUse 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  gson 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  guessPackageType 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  guessTargetEcu 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  invoke 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  isUpgradePackage 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  java 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  joinToString 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  	lowercase 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  
mutableListOf 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  parseUpgradeConfig 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  readText 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  scanUpgradePackages 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  setOf 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  	substring 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  sumOf 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  supportedExtensions 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  
toMutableList 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  use 5com.seres.usb.upgrade.analyzer.UpgradePackageAnalyzer  ActivityMainBinding !com.seres.usb.upgrade.databinding  btnRefreshStorage 5com.seres.usb.upgrade.databinding.ActivityMainBinding  getROOT 5com.seres.usb.upgrade.databinding.ActivityMainBinding  getRoot 5com.seres.usb.upgrade.databinding.ActivityMainBinding  inflate 5com.seres.usb.upgrade.databinding.ActivityMainBinding  recyclerViewStorageDevices 5com.seres.usb.upgrade.databinding.ActivityMainBinding  root 5com.seres.usb.upgrade.databinding.ActivityMainBinding  setRoot 5com.seres.usb.upgrade.databinding.ActivityMainBinding  tvStorageStatus 5com.seres.usb.upgrade.databinding.ActivityMainBinding  Boolean com.seres.usb.upgrade.manager  	ByteArray com.seres.usb.upgrade.manager  ConcurrentHashMap com.seres.usb.upgrade.manager  	Exception com.seres.usb.upgrade.manager  	Executors com.seres.usb.upgrade.manager  File com.seres.usb.upgrade.manager  FileInputStream com.seres.usb.upgrade.manager  FileOutputStream com.seres.usb.upgrade.manager  Gson com.seres.usb.upgrade.manager  Int com.seres.usb.upgrade.manager  List com.seres.usb.upgrade.manager  LogUtils com.seres.usb.upgrade.manager  String com.seres.usb.upgrade.manager  UpgradePackageAnalyzer com.seres.usb.upgrade.manager  UpgradeTaskManager com.seres.usb.upgrade.manager  UpgradeTaskPublisher com.seres.usb.upgrade.manager  UpgradeTaskStatus com.seres.usb.upgrade.manager  Volatile com.seres.usb.upgrade.manager  also com.seres.usb.upgrade.manager  equals com.seres.usb.upgrade.manager  filter com.seres.usb.upgrade.manager  invoke com.seres.usb.upgrade.manager  isEmpty com.seres.usb.upgrade.manager  let com.seres.usb.upgrade.manager  
plusAssign com.seres.usb.upgrade.manager  set com.seres.usb.upgrade.manager  synchronized com.seres.usb.upgrade.manager  toList com.seres.usb.upgrade.manager  use com.seres.usb.upgrade.manager  	writeText com.seres.usb.upgrade.manager  Boolean 0com.seres.usb.upgrade.manager.UpgradeTaskManager  	ByteArray 0com.seres.usb.upgrade.manager.UpgradeTaskManager  	Companion 0com.seres.usb.upgrade.manager.UpgradeTaskManager  ConcurrentHashMap 0com.seres.usb.upgrade.manager.UpgradeTaskManager  Context 0com.seres.usb.upgrade.manager.UpgradeTaskManager  	Exception 0com.seres.usb.upgrade.manager.UpgradeTaskManager  	Executors 0com.seres.usb.upgrade.manager.UpgradeTaskManager  File 0com.seres.usb.upgrade.manager.UpgradeTaskManager  FileInputStream 0com.seres.usb.upgrade.manager.UpgradeTaskManager  FileOutputStream 0com.seres.usb.upgrade.manager.UpgradeTaskManager  Gson 0com.seres.usb.upgrade.manager.UpgradeTaskManager  Int 0com.seres.usb.upgrade.manager.UpgradeTaskManager  List 0com.seres.usb.upgrade.manager.UpgradeTaskManager  LogUtils 0com.seres.usb.upgrade.manager.UpgradeTaskManager  String 0com.seres.usb.upgrade.manager.UpgradeTaskManager  TAG 0com.seres.usb.upgrade.manager.UpgradeTaskManager  UpgradePackageAnalyzer 0com.seres.usb.upgrade.manager.UpgradeTaskManager  UpgradeTaskInfo 0com.seres.usb.upgrade.manager.UpgradeTaskManager  UpgradeTaskManager 0com.seres.usb.upgrade.manager.UpgradeTaskManager  UpgradeTaskPublisher 0com.seres.usb.upgrade.manager.UpgradeTaskManager  UpgradeTaskStatus 0com.seres.usb.upgrade.manager.UpgradeTaskManager  Volatile 0com.seres.usb.upgrade.manager.UpgradeTaskManager  activeTasks 0com.seres.usb.upgrade.manager.UpgradeTaskManager  also 0com.seres.usb.upgrade.manager.UpgradeTaskManager  cancelTasksByUsbPath 0com.seres.usb.upgrade.manager.UpgradeTaskManager  completeTask 0com.seres.usb.upgrade.manager.UpgradeTaskManager  context 0com.seres.usb.upgrade.manager.UpgradeTaskManager  copyUpgradeFiles 0com.seres.usb.upgrade.manager.UpgradeTaskManager  distributeTask 0com.seres.usb.upgrade.manager.UpgradeTaskManager  equals 0com.seres.usb.upgrade.manager.UpgradeTaskManager  failTask 0com.seres.usb.upgrade.manager.UpgradeTaskManager  filter 0com.seres.usb.upgrade.manager.UpgradeTaskManager  generateTaskJson 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getALSO 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getActiveTasks 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getAlso 0com.seres.usb.upgrade.manager.UpgradeTaskManager  	getEQUALS 0com.seres.usb.upgrade.manager.UpgradeTaskManager  	getEquals 0com.seres.usb.upgrade.manager.UpgradeTaskManager  	getFILTER 0com.seres.usb.upgrade.manager.UpgradeTaskManager  	getFilter 0com.seres.usb.upgrade.manager.UpgradeTaskManager  
getISEmpty 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getInstance 0com.seres.usb.upgrade.manager.UpgradeTaskManager  
getIsEmpty 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getLET 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getLet 0com.seres.usb.upgrade.manager.UpgradeTaskManager  
getPLUSAssign 0com.seres.usb.upgrade.manager.UpgradeTaskManager  
getPlusAssign 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getSET 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getSet 0com.seres.usb.upgrade.manager.UpgradeTaskManager  	getTOList 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getTask 0com.seres.usb.upgrade.manager.UpgradeTaskManager  	getToList 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getUSE 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getUse 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getWRITEText 0com.seres.usb.upgrade.manager.UpgradeTaskManager  getWriteText 0com.seres.usb.upgrade.manager.UpgradeTaskManager  gson 0com.seres.usb.upgrade.manager.UpgradeTaskManager  invoke 0com.seres.usb.upgrade.manager.UpgradeTaskManager  isEmpty 0com.seres.usb.upgrade.manager.UpgradeTaskManager  let 0com.seres.usb.upgrade.manager.UpgradeTaskManager  
plusAssign 0com.seres.usb.upgrade.manager.UpgradeTaskManager  processUpgradeTask 0com.seres.usb.upgrade.manager.UpgradeTaskManager  set 0com.seres.usb.upgrade.manager.UpgradeTaskManager  synchronized 0com.seres.usb.upgrade.manager.UpgradeTaskManager  
threadPool 0com.seres.usb.upgrade.manager.UpgradeTaskManager  toList 0com.seres.usb.upgrade.manager.UpgradeTaskManager  updateTaskStatus 0com.seres.usb.upgrade.manager.UpgradeTaskManager  upgradeTaskPublisher 0com.seres.usb.upgrade.manager.UpgradeTaskManager  use 0com.seres.usb.upgrade.manager.UpgradeTaskManager  verifyFileIntegrity 0com.seres.usb.upgrade.manager.UpgradeTaskManager  	writeText 0com.seres.usb.upgrade.manager.UpgradeTaskManager  Boolean :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  	ByteArray :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  ConcurrentHashMap :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  Context :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  	Exception :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  	Executors :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  File :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  FileInputStream :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  FileOutputStream :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  Gson :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  INSTANCE :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  Int :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  List :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  LogUtils :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  String :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  UpgradePackageAnalyzer :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  UpgradeTaskInfo :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  UpgradeTaskManager :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  UpgradeTaskPublisher :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  UpgradeTaskStatus :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  Volatile :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  also :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  equals :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  filter :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getALSO :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getAlso :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  	getEQUALS :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  	getEquals :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  	getFILTER :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  	getFilter :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  
getISEmpty :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getInstance :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  
getIsEmpty :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getLET :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getLet :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  
getPLUSAssign :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  
getPlusAssign :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getSET :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getSYNCHRONIZED :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getSet :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getSynchronized :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  	getTOList :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  	getToList :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getUSE :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getUse :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getWRITEText :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  getWriteText :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  invoke :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  isEmpty :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  let :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  
plusAssign :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  set :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  synchronized :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  toList :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  use :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  	writeText :com.seres.usb.upgrade.manager.UpgradeTaskManager.Companion  Array com.seres.usb.upgrade.model  Int com.seres.usb.upgrade.model  Long com.seres.usb.upgrade.model  MutableList com.seres.usb.upgrade.model  String com.seres.usb.upgrade.model  System com.seres.usb.upgrade.model  UpgradePackageInfo com.seres.usb.upgrade.model  UpgradeTaskInfo com.seres.usb.upgrade.model  UpgradeTaskStatus com.seres.usb.upgrade.model  arrayOfNulls com.seres.usb.upgrade.model  invoke com.seres.usb.upgrade.model  
mutableListOf com.seres.usb.upgrade.model  Array .com.seres.usb.upgrade.model.UpgradePackageInfo  CREATOR .com.seres.usb.upgrade.model.UpgradePackageInfo  Int .com.seres.usb.upgrade.model.UpgradePackageInfo  Long .com.seres.usb.upgrade.model.UpgradePackageInfo  Parcel .com.seres.usb.upgrade.model.UpgradePackageInfo  
Parcelable .com.seres.usb.upgrade.model.UpgradePackageInfo  String .com.seres.usb.upgrade.model.UpgradePackageInfo  UpgradePackageInfo .com.seres.usb.upgrade.model.UpgradePackageInfo  arrayOfNulls .com.seres.usb.upgrade.model.UpgradePackageInfo  checksum .com.seres.usb.upgrade.model.UpgradePackageInfo  equals .com.seres.usb.upgrade.model.UpgradePackageInfo  invoke .com.seres.usb.upgrade.model.UpgradePackageInfo  packageName .com.seres.usb.upgrade.model.UpgradePackageInfo  packagePath .com.seres.usb.upgrade.model.UpgradePackageInfo  packageSize .com.seres.usb.upgrade.model.UpgradePackageInfo  packageType .com.seres.usb.upgrade.model.UpgradePackageInfo  packageVersion .com.seres.usb.upgrade.model.UpgradePackageInfo  	targetEcu .com.seres.usb.upgrade.model.UpgradePackageInfo  Array 6com.seres.usb.upgrade.model.UpgradePackageInfo.CREATOR  CREATOR 6com.seres.usb.upgrade.model.UpgradePackageInfo.CREATOR  Int 6com.seres.usb.upgrade.model.UpgradePackageInfo.CREATOR  Long 6com.seres.usb.upgrade.model.UpgradePackageInfo.CREATOR  Parcel 6com.seres.usb.upgrade.model.UpgradePackageInfo.CREATOR  String 6com.seres.usb.upgrade.model.UpgradePackageInfo.CREATOR  UpgradePackageInfo 6com.seres.usb.upgrade.model.UpgradePackageInfo.CREATOR  arrayOfNulls 6com.seres.usb.upgrade.model.UpgradePackageInfo.CREATOR  getARRAYOfNulls 6com.seres.usb.upgrade.model.UpgradePackageInfo.CREATOR  getArrayOfNulls 6com.seres.usb.upgrade.model.UpgradePackageInfo.CREATOR  invoke 6com.seres.usb.upgrade.model.UpgradePackageInfo.CREATOR  Array +com.seres.usb.upgrade.model.UpgradeTaskInfo  Int +com.seres.usb.upgrade.model.UpgradeTaskInfo  Long +com.seres.usb.upgrade.model.UpgradeTaskInfo  MutableList +com.seres.usb.upgrade.model.UpgradeTaskInfo  Parcel +com.seres.usb.upgrade.model.UpgradeTaskInfo  
Parcelable +com.seres.usb.upgrade.model.UpgradeTaskInfo  String +com.seres.usb.upgrade.model.UpgradeTaskInfo  System +com.seres.usb.upgrade.model.UpgradeTaskInfo  UpgradePackageInfo +com.seres.usb.upgrade.model.UpgradeTaskInfo  UpgradeTaskInfo +com.seres.usb.upgrade.model.UpgradeTaskInfo  UpgradeTaskStatus +com.seres.usb.upgrade.model.UpgradeTaskInfo  arrayOfNulls +com.seres.usb.upgrade.model.UpgradeTaskInfo  
createTime +com.seres.usb.upgrade.model.UpgradeTaskInfo  description +com.seres.usb.upgrade.model.UpgradeTaskInfo  equals +com.seres.usb.upgrade.model.UpgradeTaskInfo  getLET +com.seres.usb.upgrade.model.UpgradeTaskInfo  getLet +com.seres.usb.upgrade.model.UpgradeTaskInfo  getMUTABLEListOf +com.seres.usb.upgrade.model.UpgradeTaskInfo  getMutableListOf +com.seres.usb.upgrade.model.UpgradeTaskInfo  invoke +com.seres.usb.upgrade.model.UpgradeTaskInfo  let +com.seres.usb.upgrade.model.UpgradeTaskInfo  
mutableListOf +com.seres.usb.upgrade.model.UpgradeTaskInfo  packageList +com.seres.usb.upgrade.model.UpgradeTaskInfo  
targetPath +com.seres.usb.upgrade.model.UpgradeTaskInfo  taskId +com.seres.usb.upgrade.model.UpgradeTaskInfo  taskName +com.seres.usb.upgrade.model.UpgradeTaskInfo  
taskStatus +com.seres.usb.upgrade.model.UpgradeTaskInfo  taskVersion +com.seres.usb.upgrade.model.UpgradeTaskInfo  	totalSize +com.seres.usb.upgrade.model.UpgradeTaskInfo  usbPath +com.seres.usb.upgrade.model.UpgradeTaskInfo  Array 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  Int 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  Long 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  MutableList 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  Parcel 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  String 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  System 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  UpgradePackageInfo 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  UpgradeTaskInfo 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  UpgradeTaskStatus 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  arrayOfNulls 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  getARRAYOfNulls 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  getArrayOfNulls 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  getMUTABLEListOf 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  getMutableListOf 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  invoke 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  
mutableListOf 3com.seres.usb.upgrade.model.UpgradeTaskInfo.CREATOR  	CANCELLED -com.seres.usb.upgrade.model.UpgradeTaskStatus  	COMPLETED -com.seres.usb.upgrade.model.UpgradeTaskStatus  COPYING -com.seres.usb.upgrade.model.UpgradeTaskStatus  DISTRIBUTING -com.seres.usb.upgrade.model.UpgradeTaskStatus  FAILED -com.seres.usb.upgrade.model.UpgradeTaskStatus  PENDING -com.seres.usb.upgrade.model.UpgradeTaskStatus  READY -com.seres.usb.upgrade.model.UpgradeTaskStatus  name -com.seres.usb.upgrade.model.UpgradeTaskStatus  valueOf -com.seres.usb.upgrade.model.UpgradeTaskStatus  Boolean com.seres.usb.upgrade.publisher  Bundle com.seres.usb.upgrade.publisher  Context com.seres.usb.upgrade.publisher  	Exception com.seres.usb.upgrade.publisher  	Executors com.seres.usb.upgrade.publisher  Gson com.seres.usb.upgrade.publisher  IS2SService com.seres.usb.upgrade.publisher  Intent com.seres.usb.upgrade.publisher  LogUtils com.seres.usb.upgrade.publisher  S2S_SERVICE_ACTION com.seres.usb.upgrade.publisher  S2S_SERVICE_PACKAGE com.seres.usb.upgrade.publisher  String com.seres.usb.upgrade.publisher  System com.seres.usb.upgrade.publisher  TAG com.seres.usb.upgrade.publisher  Thread com.seres.usb.upgrade.publisher  UPGRADE_APP_ID com.seres.usb.upgrade.publisher  UPGRADE_STATUS_SERVICE_HASH com.seres.usb.upgrade.publisher  UPGRADE_TASK_SERVICE_HASH com.seres.usb.upgrade.publisher  UpgradeTaskPublisher com.seres.usb.upgrade.publisher  apply com.seres.usb.upgrade.publisher  isServiceConnected com.seres.usb.upgrade.publisher  mapOf com.seres.usb.upgrade.publisher  
s2sService com.seres.usb.upgrade.publisher  to com.seres.usb.upgrade.publisher  Boolean 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  Bundle 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  
ComponentName 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  Context 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  	Exception 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  	Executors 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  Gson 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  IAsyncResultCallback 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  IBinder 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  IS2SService 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  Intent 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  LogUtils 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  S2S_SERVICE_ACTION 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  S2S_SERVICE_PACKAGE 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  ServiceConnection 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  String 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  System 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  TAG 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  Thread 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  UPGRADE_APP_ID 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  UPGRADE_STATUS_SERVICE_HASH 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  UPGRADE_TASK_SERVICE_HASH 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  UpgradeTaskInfo 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  apply 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  connectToS2SService 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  context 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  getAPPLY 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  getApply 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  getMAPOf 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  getMapOf 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  getTO 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  getTo 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  gson 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  isServiceConnected 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  mapOf 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  publishTaskCompleted 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  publishTaskFailed 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  publishTaskStatus 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  publishUpgradeTask 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  
s2sService 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  serviceConnection 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  
threadPool 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  to 4com.seres.usb.upgrade.publisher.UpgradeTaskPublisher  Boolean >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  Bundle >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  
ComponentName >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  Context >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  	Exception >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  	Executors >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  Gson >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  IAsyncResultCallback >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  IBinder >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  IS2SService >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  Intent >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  LogUtils >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  S2S_SERVICE_ACTION >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  S2S_SERVICE_PACKAGE >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  ServiceConnection >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  String >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  System >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  TAG >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  Thread >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  UPGRADE_APP_ID >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  UPGRADE_STATUS_SERVICE_HASH >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  UPGRADE_TASK_SERVICE_HASH >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  UpgradeTaskInfo >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  apply >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  getAPPLY >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  getApply >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  getMAPOf >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  getMapOf >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  getTO >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  getTo >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  invoke >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  isServiceConnected >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  mapOf >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  
s2sService >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  to >com.seres.usb.upgrade.publisher.UpgradeTaskPublisher.Companion  getISServiceConnected Ycom.seres.usb.upgrade.publisher.UpgradeTaskPublisher.serviceConnection.<no name provided>  getIsServiceConnected Ycom.seres.usb.upgrade.publisher.UpgradeTaskPublisher.serviceConnection.<no name provided>  
getS2sService Ycom.seres.usb.upgrade.publisher.UpgradeTaskPublisher.serviceConnection.<no name provided>  isServiceConnected Ycom.seres.usb.upgrade.publisher.UpgradeTaskPublisher.serviceConnection.<no name provided>  BootCompleteReceiver com.seres.usb.upgrade.receiver  	Exception com.seres.usb.upgrade.receiver  Intent com.seres.usb.upgrade.receiver  LogUtils com.seres.usb.upgrade.receiver  UsbDetectionService com.seres.usb.upgrade.receiver  java com.seres.usb.upgrade.receiver  Context 3com.seres.usb.upgrade.receiver.BootCompleteReceiver  	Exception 3com.seres.usb.upgrade.receiver.BootCompleteReceiver  Intent 3com.seres.usb.upgrade.receiver.BootCompleteReceiver  LogUtils 3com.seres.usb.upgrade.receiver.BootCompleteReceiver  TAG 3com.seres.usb.upgrade.receiver.BootCompleteReceiver  UsbDetectionService 3com.seres.usb.upgrade.receiver.BootCompleteReceiver  java 3com.seres.usb.upgrade.receiver.BootCompleteReceiver  ACTION_STORAGE_STATUS_CHANGED com.seres.usb.upgrade.service  Boolean com.seres.usb.upgrade.service  
CHANNEL_ID com.seres.usb.upgrade.service  Context com.seres.usb.upgrade.service  EXTRA_STORAGE_DEVICES com.seres.usb.upgrade.service  EXTRA_USB_CONNECTED com.seres.usb.upgrade.service  EXTRA_USB_PATH com.seres.usb.upgrade.service  	Exception com.seres.usb.upgrade.service  	Executors com.seres.usb.upgrade.service  File com.seres.usb.upgrade.service  Int com.seres.usb.upgrade.service  Intent com.seres.usb.upgrade.service  IntentFilter com.seres.usb.upgrade.service  List com.seres.usb.upgrade.service  LogUtils com.seres.usb.upgrade.service  NOTIFICATION_ID com.seres.usb.upgrade.service  NotificationChannel com.seres.usb.upgrade.service  NotificationCompat com.seres.usb.upgrade.service  NotificationManager com.seres.usb.upgrade.service  R com.seres.usb.upgrade.service  START_STICKY com.seres.usb.upgrade.service  StorageStatusManager com.seres.usb.upgrade.service  String com.seres.usb.upgrade.service  TAG com.seres.usb.upgrade.service  UpgradePackageAnalyzer com.seres.usb.upgrade.service  UpgradeTaskManager com.seres.usb.upgrade.service  UpgradeTaskService com.seres.usb.upgrade.service  UsbDetectionService com.seres.usb.upgrade.service  apply com.seres.usb.upgrade.service  broadcastStorageStatus com.seres.usb.upgrade.service  	emptyList com.seres.usb.upgrade.service  handleUsbMounted com.seres.usb.upgrade.service  handleUsbUnmounted com.seres.usb.upgrade.service  
isNotEmpty com.seres.usb.upgrade.service  	javaClass com.seres.usb.upgrade.service  let com.seres.usb.upgrade.service  updateNotification com.seres.usb.upgrade.service  Binder 0com.seres.usb.upgrade.service.UpgradeTaskService  Boolean 0com.seres.usb.upgrade.service.UpgradeTaskService  IBinder 0com.seres.usb.upgrade.service.UpgradeTaskService  Intent 0com.seres.usb.upgrade.service.UpgradeTaskService  List 0com.seres.usb.upgrade.service.UpgradeTaskService  LogUtils 0com.seres.usb.upgrade.service.UpgradeTaskService  String 0com.seres.usb.upgrade.service.UpgradeTaskService  TAG 0com.seres.usb.upgrade.service.UpgradeTaskService  UpgradeTaskBinder 0com.seres.usb.upgrade.service.UpgradeTaskService  UpgradeTaskInfo 0com.seres.usb.upgrade.service.UpgradeTaskService  UpgradeTaskManager 0com.seres.usb.upgrade.service.UpgradeTaskService  UpgradeTaskService 0com.seres.usb.upgrade.service.UpgradeTaskService  binder 0com.seres.usb.upgrade.service.UpgradeTaskService  	emptyList 0com.seres.usb.upgrade.service.UpgradeTaskService  getEMPTYList 0com.seres.usb.upgrade.service.UpgradeTaskService  getEmptyList 0com.seres.usb.upgrade.service.UpgradeTaskService  upgradeTaskManager 0com.seres.usb.upgrade.service.UpgradeTaskService  UpgradeTaskService Bcom.seres.usb.upgrade.service.UpgradeTaskService.UpgradeTaskBinder  ACTION_STORAGE_STATUS_CHANGED 1com.seres.usb.upgrade.service.UsbDetectionService  BroadcastReceiver 1com.seres.usb.upgrade.service.UsbDetectionService  
CHANNEL_ID 1com.seres.usb.upgrade.service.UsbDetectionService  	Companion 1com.seres.usb.upgrade.service.UsbDetectionService  Context 1com.seres.usb.upgrade.service.UsbDetectionService  EXTRA_STORAGE_DEVICES 1com.seres.usb.upgrade.service.UsbDetectionService  EXTRA_USB_CONNECTED 1com.seres.usb.upgrade.service.UsbDetectionService  EXTRA_USB_PATH 1com.seres.usb.upgrade.service.UsbDetectionService  	Exception 1com.seres.usb.upgrade.service.UsbDetectionService  	Executors 1com.seres.usb.upgrade.service.UsbDetectionService  File 1com.seres.usb.upgrade.service.UsbDetectionService  IBinder 1com.seres.usb.upgrade.service.UsbDetectionService  Int 1com.seres.usb.upgrade.service.UsbDetectionService  Intent 1com.seres.usb.upgrade.service.UsbDetectionService  IntentFilter 1com.seres.usb.upgrade.service.UsbDetectionService  List 1com.seres.usb.upgrade.service.UsbDetectionService  LogUtils 1com.seres.usb.upgrade.service.UsbDetectionService  NOTIFICATION_ID 1com.seres.usb.upgrade.service.UsbDetectionService  Notification 1com.seres.usb.upgrade.service.UsbDetectionService  NotificationChannel 1com.seres.usb.upgrade.service.UsbDetectionService  NotificationCompat 1com.seres.usb.upgrade.service.UsbDetectionService  NotificationManager 1com.seres.usb.upgrade.service.UsbDetectionService  R 1com.seres.usb.upgrade.service.UsbDetectionService  START_STICKY 1com.seres.usb.upgrade.service.UsbDetectionService  StorageManager 1com.seres.usb.upgrade.service.UsbDetectionService  StorageStatusManager 1com.seres.usb.upgrade.service.UsbDetectionService  
StorageVolume 1com.seres.usb.upgrade.service.UsbDetectionService  String 1com.seres.usb.upgrade.service.UsbDetectionService  TAG 1com.seres.usb.upgrade.service.UsbDetectionService  UpgradePackageAnalyzer 1com.seres.usb.upgrade.service.UsbDetectionService  UpgradeTaskManager 1com.seres.usb.upgrade.service.UsbDetectionService  apply 1com.seres.usb.upgrade.service.UsbDetectionService  broadcastStorageStatus 1com.seres.usb.upgrade.service.UsbDetectionService  checkExistingUsbDevices 1com.seres.usb.upgrade.service.UsbDetectionService  createNotification 1com.seres.usb.upgrade.service.UsbDetectionService  createNotificationChannel 1com.seres.usb.upgrade.service.UsbDetectionService  	emptyList 1com.seres.usb.upgrade.service.UsbDetectionService  getAPPLY 1com.seres.usb.upgrade.service.UsbDetectionService  getApply 1com.seres.usb.upgrade.service.UsbDetectionService  getEMPTYList 1com.seres.usb.upgrade.service.UsbDetectionService  getEmptyList 1com.seres.usb.upgrade.service.UsbDetectionService  
getISNotEmpty 1com.seres.usb.upgrade.service.UsbDetectionService  
getIsNotEmpty 1com.seres.usb.upgrade.service.UsbDetectionService  getLET 1com.seres.usb.upgrade.service.UsbDetectionService  getLet 1com.seres.usb.upgrade.service.UsbDetectionService  getSystemService 1com.seres.usb.upgrade.service.UsbDetectionService  
getVolumePath 1com.seres.usb.upgrade.service.UsbDetectionService  handleUsbMounted 1com.seres.usb.upgrade.service.UsbDetectionService  handleUsbUnmounted 1com.seres.usb.upgrade.service.UsbDetectionService  
isNotEmpty 1com.seres.usb.upgrade.service.UsbDetectionService  	javaClass 1com.seres.usb.upgrade.service.UsbDetectionService  let 1com.seres.usb.upgrade.service.UsbDetectionService  registerReceiver 1com.seres.usb.upgrade.service.UsbDetectionService  registerUsbReceiver 1com.seres.usb.upgrade.service.UsbDetectionService  
sendBroadcast 1com.seres.usb.upgrade.service.UsbDetectionService  setupStorageStatusListener 1com.seres.usb.upgrade.service.UsbDetectionService  startForeground 1com.seres.usb.upgrade.service.UsbDetectionService  storageStatusManager 1com.seres.usb.upgrade.service.UsbDetectionService  
threadPool 1com.seres.usb.upgrade.service.UsbDetectionService  unregisterReceiver 1com.seres.usb.upgrade.service.UsbDetectionService  updateNotification 1com.seres.usb.upgrade.service.UsbDetectionService  upgradeTaskManager 1com.seres.usb.upgrade.service.UsbDetectionService  usbReceiver 1com.seres.usb.upgrade.service.UsbDetectionService  ACTION_STORAGE_STATUS_CHANGED ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  BroadcastReceiver ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  
CHANNEL_ID ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  Context ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  EXTRA_STORAGE_DEVICES ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  EXTRA_USB_CONNECTED ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  EXTRA_USB_PATH ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  	Exception ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  	Executors ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  File ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  IBinder ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  Int ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  Intent ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  IntentFilter ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  List ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  LogUtils ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  NOTIFICATION_ID ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  Notification ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  NotificationChannel ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  NotificationCompat ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  NotificationManager ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  R ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  START_STICKY ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  StorageManager ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  StorageStatusManager ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  
StorageVolume ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  String ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  TAG ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  UpgradePackageAnalyzer ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  UpgradeTaskManager ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  apply ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  broadcastStorageStatus ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  	emptyList ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  getAPPLY ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  getApply ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  getEMPTYList ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  getEmptyList ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  
getISNotEmpty ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  
getIsNotEmpty ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  getLET ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  getLet ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  handleUsbMounted ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  handleUsbUnmounted ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  
isNotEmpty ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  	javaClass ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  let ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  updateNotification ;com.seres.usb.upgrade.service.UsbDetectionService.Companion  getBROADCASTStorageStatus _com.seres.usb.upgrade.service.UsbDetectionService.setupStorageStatusListener.<no name provided>  getBroadcastStorageStatus _com.seres.usb.upgrade.service.UsbDetectionService.setupStorageStatusListener.<no name provided>  getUPDATENotification _com.seres.usb.upgrade.service.UsbDetectionService.setupStorageStatusListener.<no name provided>  getUpdateNotification _com.seres.usb.upgrade.service.UsbDetectionService.setupStorageStatusListener.<no name provided>  getHANDLEUsbMounted Pcom.seres.usb.upgrade.service.UsbDetectionService.usbReceiver.<no name provided>  getHANDLEUsbUnmounted Pcom.seres.usb.upgrade.service.UsbDetectionService.usbReceiver.<no name provided>  getHandleUsbMounted Pcom.seres.usb.upgrade.service.UsbDetectionService.usbReceiver.<no name provided>  getHandleUsbUnmounted Pcom.seres.usb.upgrade.service.UsbDetectionService.usbReceiver.<no name provided>  getLET Pcom.seres.usb.upgrade.service.UsbDetectionService.usbReceiver.<no name provided>  getLet Pcom.seres.usb.upgrade.service.UsbDetectionService.usbReceiver.<no name provided>  Boolean com.seres.usb.upgrade.storage  Context com.seres.usb.upgrade.storage  Double com.seres.usb.upgrade.storage  Environment com.seres.usb.upgrade.storage  	Exception com.seres.usb.upgrade.storage  File com.seres.usb.upgrade.storage  Int com.seres.usb.upgrade.storage  List com.seres.usb.upgrade.storage  LogUtils com.seres.usb.upgrade.storage  Long com.seres.usb.upgrade.storage  StorageDeviceInfo com.seres.usb.upgrade.storage  StorageStatusManager com.seres.usb.upgrade.storage  String com.seres.usb.upgrade.storage  filter com.seres.usb.upgrade.storage  format com.seres.usb.upgrade.storage  
isNotEmpty com.seres.usb.upgrade.storage  
isNullOrEmpty com.seres.usb.upgrade.storage  	javaClass com.seres.usb.upgrade.storage  
mutableListOf com.seres.usb.upgrade.storage  Boolean 2com.seres.usb.upgrade.storage.StorageStatusManager  Context 2com.seres.usb.upgrade.storage.StorageStatusManager  Double 2com.seres.usb.upgrade.storage.StorageStatusManager  Environment 2com.seres.usb.upgrade.storage.StorageStatusManager  	Exception 2com.seres.usb.upgrade.storage.StorageStatusManager  File 2com.seres.usb.upgrade.storage.StorageStatusManager  Int 2com.seres.usb.upgrade.storage.StorageStatusManager  List 2com.seres.usb.upgrade.storage.StorageStatusManager  LogUtils 2com.seres.usb.upgrade.storage.StorageStatusManager  Long 2com.seres.usb.upgrade.storage.StorageStatusManager  StorageDeviceInfo 2com.seres.usb.upgrade.storage.StorageStatusManager  StorageManager 2com.seres.usb.upgrade.storage.StorageStatusManager  StorageStatusListener 2com.seres.usb.upgrade.storage.StorageStatusManager  
StorageVolume 2com.seres.usb.upgrade.storage.StorageStatusManager  String 2com.seres.usb.upgrade.storage.StorageStatusManager  TAG 2com.seres.usb.upgrade.storage.StorageStatusManager  context 2com.seres.usb.upgrade.storage.StorageStatusManager  filter 2com.seres.usb.upgrade.storage.StorageStatusManager  format 2com.seres.usb.upgrade.storage.StorageStatusManager  formatStorageSize 2com.seres.usb.upgrade.storage.StorageStatusManager  getAllStorageDevices 2com.seres.usb.upgrade.storage.StorageStatusManager  	getFILTER 2com.seres.usb.upgrade.storage.StorageStatusManager  	getFORMAT 2com.seres.usb.upgrade.storage.StorageStatusManager  	getFilter 2com.seres.usb.upgrade.storage.StorageStatusManager  getFirstUsbDevicePath 2com.seres.usb.upgrade.storage.StorageStatusManager  	getFormat 2com.seres.usb.upgrade.storage.StorageStatusManager  
getISNotEmpty 2com.seres.usb.upgrade.storage.StorageStatusManager  getISNullOrEmpty 2com.seres.usb.upgrade.storage.StorageStatusManager  getInternalStorageInfo 2com.seres.usb.upgrade.storage.StorageStatusManager  
getIsNotEmpty 2com.seres.usb.upgrade.storage.StorageStatusManager  getIsNullOrEmpty 2com.seres.usb.upgrade.storage.StorageStatusManager  getMUTABLEListOf 2com.seres.usb.upgrade.storage.StorageStatusManager  getMutableListOf 2com.seres.usb.upgrade.storage.StorageStatusManager  getRemovableStorageDevices 2com.seres.usb.upgrade.storage.StorageStatusManager  getStorageDeviceInfo 2com.seres.usb.upgrade.storage.StorageStatusManager  getVolumeDescription 2com.seres.usb.upgrade.storage.StorageStatusManager  
getVolumePath 2com.seres.usb.upgrade.storage.StorageStatusManager  hasUsbDeviceConnected 2com.seres.usb.upgrade.storage.StorageStatusManager  
isNotEmpty 2com.seres.usb.upgrade.storage.StorageStatusManager  
isNullOrEmpty 2com.seres.usb.upgrade.storage.StorageStatusManager  isPathAccessible 2com.seres.usb.upgrade.storage.StorageStatusManager  	javaClass 2com.seres.usb.upgrade.storage.StorageStatusManager  
mutableListOf 2com.seres.usb.upgrade.storage.StorageStatusManager  refreshStorageStatus 2com.seres.usb.upgrade.storage.StorageStatusManager  setStatusListener 2com.seres.usb.upgrade.storage.StorageStatusManager  statusListener 2com.seres.usb.upgrade.storage.StorageStatusManager  Boolean Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  Double Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  Int Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  Long Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  String Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  description Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  equals Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  	freeSpace Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  isRemovable Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  path Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  state Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  
totalSpace Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  usagePercentage Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  	usedSpace Dcom.seres.usb.upgrade.storage.StorageStatusManager.StorageDeviceInfo  List Hcom.seres.usb.upgrade.storage.StorageStatusManager.StorageStatusListener  StorageDeviceInfo Hcom.seres.usb.upgrade.storage.StorageStatusManager.StorageStatusListener  String Hcom.seres.usb.upgrade.storage.StorageStatusManager.StorageStatusListener  onStorageStatusChanged Hcom.seres.usb.upgrade.storage.StorageStatusManager.StorageStatusListener  Boolean com.seres.usb.upgrade.utils  Date com.seres.usb.upgrade.utils  	Exception com.seres.usb.upgrade.utils  File com.seres.usb.upgrade.utils  
FileWriter com.seres.usb.upgrade.utils  Int com.seres.usb.upgrade.utils  Locale com.seres.usb.upgrade.utils  Log com.seres.usb.upgrade.utils  LogUtils com.seres.usb.upgrade.utils  SimpleDateFormat com.seres.usb.upgrade.utils  String com.seres.usb.upgrade.utils  System com.seres.usb.upgrade.utils  	Throwable com.seres.usb.upgrade.utils  forEach com.seres.usb.upgrade.utils  use com.seres.usb.upgrade.utils  Boolean $com.seres.usb.upgrade.utils.LogUtils  Context $com.seres.usb.upgrade.utils.LogUtils  Date $com.seres.usb.upgrade.utils.LogUtils  	Exception $com.seres.usb.upgrade.utils.LogUtils  File $com.seres.usb.upgrade.utils.LogUtils  
FileWriter $com.seres.usb.upgrade.utils.LogUtils  Int $com.seres.usb.upgrade.utils.LogUtils  Locale $com.seres.usb.upgrade.utils.LogUtils  Log $com.seres.usb.upgrade.utils.LogUtils  SimpleDateFormat $com.seres.usb.upgrade.utils.LogUtils  String $com.seres.usb.upgrade.utils.LogUtils  System $com.seres.usb.upgrade.utils.LogUtils  
TAG_PREFIX $com.seres.usb.upgrade.utils.LogUtils  	Throwable $com.seres.usb.upgrade.utils.LogUtils  d $com.seres.usb.upgrade.utils.LogUtils  
dateFormat $com.seres.usb.upgrade.utils.LogUtils  e $com.seres.usb.upgrade.utils.LogUtils  forEach $com.seres.usb.upgrade.utils.LogUtils  
getFOREach $com.seres.usb.upgrade.utils.LogUtils  
getForEach $com.seres.usb.upgrade.utils.LogUtils  getUSE $com.seres.usb.upgrade.utils.LogUtils  getUse $com.seres.usb.upgrade.utils.LogUtils  i $com.seres.usb.upgrade.utils.LogUtils  init $com.seres.usb.upgrade.utils.LogUtils  invoke $com.seres.usb.upgrade.utils.LogUtils  isDebugMode $com.seres.usb.upgrade.utils.LogUtils  logFile $com.seres.usb.upgrade.utils.LogUtils  	logToFile $com.seres.usb.upgrade.utils.LogUtils  use $com.seres.usb.upgrade.utils.LogUtils  w $com.seres.usb.upgrade.utils.LogUtils  writeToFile $com.seres.usb.upgrade.utils.LogUtils  File java.io  FileInputStream java.io  FileOutputStream java.io  
FileWriter java.io  Serializable java.io  Writer java.io  absolutePath java.io.File  canRead java.io.File  delete java.io.File  equals java.io.File  exists java.io.File  	extension java.io.File  	freeSpace java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  getEXTENSION java.io.File  getExtension java.io.File  getFREESpace java.io.File  getFreeSpace java.io.File  getISDirectory java.io.File  	getISFile java.io.File  getIsDirectory java.io.File  	getIsFile java.io.File  getNAME java.io.File  getName java.io.File  getREADText java.io.File  getReadText java.io.File  
getTOTALSpace java.io.File  
getTotalSpace java.io.File  getWRITEText java.io.File  getWriteText java.io.File  isDirectory java.io.File  isFile java.io.File  lastModified java.io.File  length java.io.File  	listFiles java.io.File  mkdirs java.io.File  name java.io.File  readText java.io.File  setAbsolutePath java.io.File  setDirectory java.io.File  setFile java.io.File  setFreeSpace java.io.File  setName java.io.File  
setTotalSpace java.io.File  
totalSpace java.io.File  	writeText java.io.File  getUSE java.io.FileInputStream  getUse java.io.FileInputStream  read java.io.FileInputStream  use java.io.FileInputStream  getUSE java.io.FileOutputStream  getUse java.io.FileOutputStream  use java.io.FileOutputStream  write java.io.FileOutputStream  append java.io.FileWriter  getUSE java.io.FileWriter  getUse java.io.FileWriter  use java.io.FileWriter  read java.io.InputStream  use java.io.InputStream  use java.io.OutputStream  write java.io.OutputStream  append java.io.OutputStreamWriter  use java.io.OutputStreamWriter  append java.io.Writer  use java.io.Writer  ACTION_STORAGE_STATUS_CHANGED 	java.lang  ActivityMainBinding 	java.lang  ActivityResultContracts 	java.lang  Bundle 	java.lang  	ByteArray 	java.lang  
CHANNEL_ID 	java.lang  Class 	java.lang  ConcurrentHashMap 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  Date 	java.lang  EXTRA_STORAGE_DEVICES 	java.lang  EXTRA_USB_CONNECTED 	java.lang  EXTRA_USB_PATH 	java.lang  Environment 	java.lang  	Exception 	java.lang  	Executors 	java.lang  File 	java.lang  FileInputStream 	java.lang  FileOutputStream 	java.lang  FileProvider 	java.lang  
FileWriter 	java.lang  Gson 	java.lang  IS2SService 	java.lang  Intent 	java.lang  IntentFilter 	java.lang  LayoutInflater 	java.lang  LinearLayoutManager 	java.lang  Locale 	java.lang  Log 	java.lang  LogUtils 	java.lang  Manifest 	java.lang  
MessageDigest 	java.lang  NOTIFICATION_ID 	java.lang  NotificationChannel 	java.lang  NotificationCompat 	java.lang  NotificationManager 	java.lang  PackageManager 	java.lang  R 	java.lang  Regex 	java.lang  S2S_SERVICE_ACTION 	java.lang  S2S_SERVICE_PACKAGE 	java.lang  START_STICKY 	java.lang  SimpleDateFormat 	java.lang  StorageDeviceAdapter 	java.lang  StorageDeviceInfo 	java.lang  StorageDeviceViewHolder 	java.lang  StorageStatusManager 	java.lang  String 	java.lang  System 	java.lang  TAG 	java.lang  Thread 	java.lang  Toast 	java.lang  UPGRADE_APP_ID 	java.lang  UPGRADE_STATUS_SERVICE_HASH 	java.lang  UPGRADE_TASK_SERVICE_HASH 	java.lang  UUID 	java.lang  
UpgradeConfig 	java.lang  UpgradePackageAnalyzer 	java.lang  UpgradePackageInfo 	java.lang  UpgradeTaskInfo 	java.lang  UpgradeTaskManager 	java.lang  UpgradeTaskPublisher 	java.lang  UpgradeTaskStatus 	java.lang  Uri 	java.lang  UsbDetectionService 	java.lang  all 	java.lang  also 	java.lang  android 	java.lang  apply 	java.lang  arrayOf 	java.lang  arrayOfNulls 	java.lang  broadcastStorageStatus 	java.lang  contains 	java.lang  	emptyList 	java.lang  equals 	java.lang  	extension 	java.lang  filter 	java.lang  find 	java.lang  forEach 	java.lang  format 	java.lang  
getUriForFile 	java.lang  handleUsbMounted 	java.lang  handleUsbUnmounted 	java.lang  invoke 	java.lang  isEmpty 	java.lang  
isInitialized 	java.lang  
isNotEmpty 	java.lang  
isNullOrEmpty 	java.lang  isServiceConnected 	java.lang  java 	java.lang  	javaClass 	java.lang  joinToString 	java.lang  let 	java.lang  	lowercase 	java.lang  mapOf 	java.lang  
mutableListOf 	java.lang  
plusAssign 	java.lang  readText 	java.lang  
s2sService 	java.lang  set 	java.lang  setOf 	java.lang  storageDeviceAdapter 	java.lang  	substring 	java.lang  sumOf 	java.lang  synchronized 	java.lang  to 	java.lang  toList 	java.lang  
toMutableList 	java.lang  toTypedArray 	java.lang  updateNotification 	java.lang  use 	java.lang  	writeText 	java.lang  	getMethod java.lang.Class  invoke java.lang.Exception  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  sleep java.lang.Thread  Method java.lang.reflect  invoke "java.lang.reflect.AccessibleObject  invoke java.lang.reflect.Executable  invoke java.lang.reflect.Method  
MessageDigest 
java.security  digest java.security.MessageDigest  getInstance java.security.MessageDigest  update java.security.MessageDigest  digest java.security.MessageDigestSpi  update java.security.MessageDigestSpi  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  	ArrayList 	java.util  	ByteArray 	java.util  Date 	java.util  	Exception 	java.util  File 	java.util  FileInputStream 	java.util  
FileWriter 	java.util  Gson 	java.util  Locale 	java.util  Log 	java.util  LogUtils 	java.util  
MessageDigest 	java.util  Regex 	java.util  SimpleDateFormat 	java.util  System 	java.util  UUID 	java.util  
UpgradeConfig 	java.util  UpgradePackageInfo 	java.util  UpgradeTaskInfo 	java.util  UpgradeTaskStatus 	java.util  also 	java.util  contains 	java.util  	emptyList 	java.util  	extension 	java.util  find 	java.util  forEach 	java.util  format 	java.util  invoke 	java.util  java 	java.util  joinToString 	java.util  	lowercase 	java.util  
mutableListOf 	java.util  readText 	java.util  setOf 	java.util  	substring 	java.util  sumOf 	java.util  
toMutableList 	java.util  use 	java.util  get java.util.AbstractMap  remove java.util.AbstractMap  set java.util.AbstractMap  
getDefault java.util.Locale  
randomUUID java.util.UUID  toString java.util.UUID  ConcurrentHashMap java.util.concurrent  ExecutorService java.util.concurrent  	Executors java.util.concurrent  Future java.util.concurrent  <SAM-CONSTRUCTOR> java.util.concurrent.Callable  get &java.util.concurrent.ConcurrentHashMap  getSET &java.util.concurrent.ConcurrentHashMap  getSet &java.util.concurrent.ConcurrentHashMap  remove &java.util.concurrent.ConcurrentHashMap  set &java.util.concurrent.ConcurrentHashMap  values &java.util.concurrent.ConcurrentHashMap  submit $java.util.concurrent.ExecutorService  newCachedThreadPool java.util.concurrent.Executors  ACTION_STORAGE_STATUS_CHANGED kotlin  ActivityMainBinding kotlin  ActivityResultContracts kotlin  Any kotlin  Array kotlin  Boolean kotlin  Bundle kotlin  Byte kotlin  	ByteArray kotlin  
CHANNEL_ID kotlin  CharSequence kotlin  ConcurrentHashMap kotlin  Context kotlin  
ContextCompat kotlin  Date kotlin  Double kotlin  EXTRA_STORAGE_DEVICES kotlin  EXTRA_USB_CONNECTED kotlin  EXTRA_USB_PATH kotlin  Environment kotlin  	Exception kotlin  	Executors kotlin  File kotlin  FileInputStream kotlin  FileOutputStream kotlin  FileProvider kotlin  
FileWriter kotlin  	Function0 kotlin  	Function1 kotlin  Gson kotlin  IS2SService kotlin  Int kotlin  Intent kotlin  IntentFilter kotlin  LayoutInflater kotlin  LinearLayoutManager kotlin  Locale kotlin  Log kotlin  LogUtils kotlin  Long kotlin  Manifest kotlin  
MessageDigest kotlin  NOTIFICATION_ID kotlin  Nothing kotlin  NotificationChannel kotlin  NotificationCompat kotlin  NotificationManager kotlin  PackageManager kotlin  Pair kotlin  R kotlin  Regex kotlin  S2S_SERVICE_ACTION kotlin  S2S_SERVICE_PACKAGE kotlin  START_STICKY kotlin  SimpleDateFormat kotlin  StorageDeviceAdapter kotlin  StorageDeviceInfo kotlin  StorageDeviceViewHolder kotlin  StorageStatusManager kotlin  String kotlin  System kotlin  TAG kotlin  Thread kotlin  	Throwable kotlin  Toast kotlin  UPGRADE_APP_ID kotlin  UPGRADE_STATUS_SERVICE_HASH kotlin  UPGRADE_TASK_SERVICE_HASH kotlin  UUID kotlin  Unit kotlin  
UpgradeConfig kotlin  UpgradePackageAnalyzer kotlin  UpgradePackageInfo kotlin  UpgradeTaskInfo kotlin  UpgradeTaskManager kotlin  UpgradeTaskPublisher kotlin  UpgradeTaskStatus kotlin  Uri kotlin  UsbDetectionService kotlin  Volatile kotlin  all kotlin  also kotlin  android kotlin  apply kotlin  arrayOf kotlin  arrayOfNulls kotlin  broadcastStorageStatus kotlin  contains kotlin  	emptyList kotlin  equals kotlin  	extension kotlin  filter kotlin  find kotlin  forEach kotlin  format kotlin  
getUriForFile kotlin  handleUsbMounted kotlin  handleUsbUnmounted kotlin  invoke kotlin  isEmpty kotlin  
isInitialized kotlin  
isNotEmpty kotlin  
isNullOrEmpty kotlin  isServiceConnected kotlin  java kotlin  	javaClass kotlin  joinToString kotlin  let kotlin  	lowercase kotlin  mapOf kotlin  
mutableListOf kotlin  
plusAssign kotlin  readText kotlin  
s2sService kotlin  set kotlin  setOf kotlin  storageDeviceAdapter kotlin  	substring kotlin  sumOf kotlin  synchronized kotlin  to kotlin  toList kotlin  
toMutableList kotlin  toTypedArray kotlin  updateNotification kotlin  use kotlin  	writeText kotlin  	getFILTER kotlin.Array  
getFOREach kotlin.Array  	getFilter kotlin.Array  
getForEach kotlin.Array  getJOINToString kotlin.ByteArray  getJoinToString kotlin.ByteArray  getALSO 
kotlin.Int  getAlso 
kotlin.Int  
getPLUSAssign kotlin.Long  
getPlusAssign kotlin.Long  getCONTAINS 
kotlin.String  getContains 
kotlin.String  	getEQUALS 
kotlin.String  	getEquals 
kotlin.String  	getFORMAT 
kotlin.String  	getFormat 
kotlin.String  
getISEmpty 
kotlin.String  getISNullOrEmpty 
kotlin.String  
getIsEmpty 
kotlin.String  getIsNullOrEmpty 
kotlin.String  getLET 
kotlin.String  getLOWERCASE 
kotlin.String  getLet 
kotlin.String  getLowercase 
kotlin.String  getSUBSTRING 
kotlin.String  getSubstring 
kotlin.String  getTO 
kotlin.String  getTo 
kotlin.String  isEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  	getFORMAT kotlin.String.Companion  	getFormat kotlin.String.Companion  ACTION_STORAGE_STATUS_CHANGED kotlin.annotation  ActivityMainBinding kotlin.annotation  ActivityResultContracts kotlin.annotation  Bundle kotlin.annotation  	ByteArray kotlin.annotation  
CHANNEL_ID kotlin.annotation  ConcurrentHashMap kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  Date kotlin.annotation  EXTRA_STORAGE_DEVICES kotlin.annotation  EXTRA_USB_CONNECTED kotlin.annotation  EXTRA_USB_PATH kotlin.annotation  Environment kotlin.annotation  	Exception kotlin.annotation  	Executors kotlin.annotation  File kotlin.annotation  FileInputStream kotlin.annotation  FileOutputStream kotlin.annotation  FileProvider kotlin.annotation  
FileWriter kotlin.annotation  Gson kotlin.annotation  IS2SService kotlin.annotation  Intent kotlin.annotation  IntentFilter kotlin.annotation  LayoutInflater kotlin.annotation  LinearLayoutManager kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  LogUtils kotlin.annotation  Manifest kotlin.annotation  
MessageDigest kotlin.annotation  NOTIFICATION_ID kotlin.annotation  NotificationChannel kotlin.annotation  NotificationCompat kotlin.annotation  NotificationManager kotlin.annotation  PackageManager kotlin.annotation  R kotlin.annotation  Regex kotlin.annotation  S2S_SERVICE_ACTION kotlin.annotation  S2S_SERVICE_PACKAGE kotlin.annotation  START_STICKY kotlin.annotation  SimpleDateFormat kotlin.annotation  StorageDeviceAdapter kotlin.annotation  StorageDeviceInfo kotlin.annotation  StorageDeviceViewHolder kotlin.annotation  StorageStatusManager kotlin.annotation  String kotlin.annotation  System kotlin.annotation  TAG kotlin.annotation  Thread kotlin.annotation  Toast kotlin.annotation  UPGRADE_APP_ID kotlin.annotation  UPGRADE_STATUS_SERVICE_HASH kotlin.annotation  UPGRADE_TASK_SERVICE_HASH kotlin.annotation  UUID kotlin.annotation  
UpgradeConfig kotlin.annotation  UpgradePackageAnalyzer kotlin.annotation  UpgradePackageInfo kotlin.annotation  UpgradeTaskInfo kotlin.annotation  UpgradeTaskManager kotlin.annotation  UpgradeTaskPublisher kotlin.annotation  UpgradeTaskStatus kotlin.annotation  Uri kotlin.annotation  UsbDetectionService kotlin.annotation  Volatile kotlin.annotation  all kotlin.annotation  also kotlin.annotation  android kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  arrayOfNulls kotlin.annotation  broadcastStorageStatus kotlin.annotation  contains kotlin.annotation  	emptyList kotlin.annotation  equals kotlin.annotation  	extension kotlin.annotation  filter kotlin.annotation  find kotlin.annotation  forEach kotlin.annotation  format kotlin.annotation  
getUriForFile kotlin.annotation  handleUsbMounted kotlin.annotation  handleUsbUnmounted kotlin.annotation  invoke kotlin.annotation  isEmpty kotlin.annotation  
isInitialized kotlin.annotation  
isNotEmpty kotlin.annotation  
isNullOrEmpty kotlin.annotation  isServiceConnected kotlin.annotation  java kotlin.annotation  	javaClass kotlin.annotation  joinToString kotlin.annotation  let kotlin.annotation  	lowercase kotlin.annotation  mapOf kotlin.annotation  
mutableListOf kotlin.annotation  
plusAssign kotlin.annotation  readText kotlin.annotation  
s2sService kotlin.annotation  set kotlin.annotation  setOf kotlin.annotation  storageDeviceAdapter kotlin.annotation  	substring kotlin.annotation  sumOf kotlin.annotation  synchronized kotlin.annotation  to kotlin.annotation  toList kotlin.annotation  
toMutableList kotlin.annotation  toTypedArray kotlin.annotation  updateNotification kotlin.annotation  use kotlin.annotation  	writeText kotlin.annotation  ACTION_STORAGE_STATUS_CHANGED kotlin.collections  ActivityMainBinding kotlin.collections  ActivityResultContracts kotlin.collections  Bundle kotlin.collections  	ByteArray kotlin.collections  
CHANNEL_ID kotlin.collections  ConcurrentHashMap kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  Date kotlin.collections  EXTRA_STORAGE_DEVICES kotlin.collections  EXTRA_USB_CONNECTED kotlin.collections  EXTRA_USB_PATH kotlin.collections  Environment kotlin.collections  	Exception kotlin.collections  	Executors kotlin.collections  File kotlin.collections  FileInputStream kotlin.collections  FileOutputStream kotlin.collections  FileProvider kotlin.collections  
FileWriter kotlin.collections  Gson kotlin.collections  IS2SService kotlin.collections  Intent kotlin.collections  IntentFilter kotlin.collections  LayoutInflater kotlin.collections  LinearLayoutManager kotlin.collections  List kotlin.collections  Locale kotlin.collections  Log kotlin.collections  LogUtils kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  
MessageDigest kotlin.collections  MutableList kotlin.collections  NOTIFICATION_ID kotlin.collections  NotificationChannel kotlin.collections  NotificationCompat kotlin.collections  NotificationManager kotlin.collections  PackageManager kotlin.collections  R kotlin.collections  Regex kotlin.collections  S2S_SERVICE_ACTION kotlin.collections  S2S_SERVICE_PACKAGE kotlin.collections  START_STICKY kotlin.collections  Set kotlin.collections  SimpleDateFormat kotlin.collections  StorageDeviceAdapter kotlin.collections  StorageDeviceInfo kotlin.collections  StorageDeviceViewHolder kotlin.collections  StorageStatusManager kotlin.collections  String kotlin.collections  System kotlin.collections  TAG kotlin.collections  Thread kotlin.collections  Toast kotlin.collections  UPGRADE_APP_ID kotlin.collections  UPGRADE_STATUS_SERVICE_HASH kotlin.collections  UPGRADE_TASK_SERVICE_HASH kotlin.collections  UUID kotlin.collections  
UpgradeConfig kotlin.collections  UpgradePackageAnalyzer kotlin.collections  UpgradePackageInfo kotlin.collections  UpgradeTaskInfo kotlin.collections  UpgradeTaskManager kotlin.collections  UpgradeTaskPublisher kotlin.collections  UpgradeTaskStatus kotlin.collections  Uri kotlin.collections  UsbDetectionService kotlin.collections  Volatile kotlin.collections  all kotlin.collections  also kotlin.collections  android kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  arrayOfNulls kotlin.collections  broadcastStorageStatus kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  equals kotlin.collections  	extension kotlin.collections  filter kotlin.collections  find kotlin.collections  forEach kotlin.collections  format kotlin.collections  
getUriForFile kotlin.collections  handleUsbMounted kotlin.collections  handleUsbUnmounted kotlin.collections  invoke kotlin.collections  isEmpty kotlin.collections  
isInitialized kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  isServiceConnected kotlin.collections  java kotlin.collections  	javaClass kotlin.collections  joinToString kotlin.collections  let kotlin.collections  	lowercase kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  
plusAssign kotlin.collections  readText kotlin.collections  
s2sService kotlin.collections  set kotlin.collections  setOf kotlin.collections  storageDeviceAdapter kotlin.collections  	substring kotlin.collections  sumOf kotlin.collections  	sumOfLong kotlin.collections  synchronized kotlin.collections  to kotlin.collections  toList kotlin.collections  
toMutableList kotlin.collections  toTypedArray kotlin.collections  updateNotification kotlin.collections  use kotlin.collections  	writeText kotlin.collections  getALL kotlin.collections.Collection  getAll kotlin.collections.Collection  	getFILTER kotlin.collections.List  getFIND kotlin.collections.List  	getFilter kotlin.collections.List  getFind kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getSUMOf kotlin.collections.List  getSumOf kotlin.collections.List  getTOMutableList kotlin.collections.List  getTOTypedArray kotlin.collections.List  getToMutableList kotlin.collections.List  getToTypedArray kotlin.collections.List  
isNotEmpty kotlin.collections.List  	getFILTER $kotlin.collections.MutableCollection  	getFilter $kotlin.collections.MutableCollection  	getTOList $kotlin.collections.MutableCollection  	getToList $kotlin.collections.MutableCollection  
getISNotEmpty kotlin.collections.MutableList  
getIsNotEmpty kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  ACTION_STORAGE_STATUS_CHANGED kotlin.comparisons  ActivityMainBinding kotlin.comparisons  ActivityResultContracts kotlin.comparisons  Bundle kotlin.comparisons  	ByteArray kotlin.comparisons  
CHANNEL_ID kotlin.comparisons  ConcurrentHashMap kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  Date kotlin.comparisons  EXTRA_STORAGE_DEVICES kotlin.comparisons  EXTRA_USB_CONNECTED kotlin.comparisons  EXTRA_USB_PATH kotlin.comparisons  Environment kotlin.comparisons  	Exception kotlin.comparisons  	Executors kotlin.comparisons  File kotlin.comparisons  FileInputStream kotlin.comparisons  FileOutputStream kotlin.comparisons  FileProvider kotlin.comparisons  
FileWriter kotlin.comparisons  Gson kotlin.comparisons  IS2SService kotlin.comparisons  Intent kotlin.comparisons  IntentFilter kotlin.comparisons  LayoutInflater kotlin.comparisons  LinearLayoutManager kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  LogUtils kotlin.comparisons  Manifest kotlin.comparisons  
MessageDigest kotlin.comparisons  NOTIFICATION_ID kotlin.comparisons  NotificationChannel kotlin.comparisons  NotificationCompat kotlin.comparisons  NotificationManager kotlin.comparisons  PackageManager kotlin.comparisons  R kotlin.comparisons  Regex kotlin.comparisons  S2S_SERVICE_ACTION kotlin.comparisons  S2S_SERVICE_PACKAGE kotlin.comparisons  START_STICKY kotlin.comparisons  SimpleDateFormat kotlin.comparisons  StorageDeviceAdapter kotlin.comparisons  StorageDeviceInfo kotlin.comparisons  StorageDeviceViewHolder kotlin.comparisons  StorageStatusManager kotlin.comparisons  String kotlin.comparisons  System kotlin.comparisons  TAG kotlin.comparisons  Thread kotlin.comparisons  Toast kotlin.comparisons  UPGRADE_APP_ID kotlin.comparisons  UPGRADE_STATUS_SERVICE_HASH kotlin.comparisons  UPGRADE_TASK_SERVICE_HASH kotlin.comparisons  UUID kotlin.comparisons  
UpgradeConfig kotlin.comparisons  UpgradePackageAnalyzer kotlin.comparisons  UpgradePackageInfo kotlin.comparisons  UpgradeTaskInfo kotlin.comparisons  UpgradeTaskManager kotlin.comparisons  UpgradeTaskPublisher kotlin.comparisons  UpgradeTaskStatus kotlin.comparisons  Uri kotlin.comparisons  UsbDetectionService kotlin.comparisons  Volatile kotlin.comparisons  all kotlin.comparisons  also kotlin.comparisons  android kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  arrayOfNulls kotlin.comparisons  broadcastStorageStatus kotlin.comparisons  contains kotlin.comparisons  	emptyList kotlin.comparisons  equals kotlin.comparisons  	extension kotlin.comparisons  filter kotlin.comparisons  find kotlin.comparisons  forEach kotlin.comparisons  format kotlin.comparisons  
getUriForFile kotlin.comparisons  handleUsbMounted kotlin.comparisons  handleUsbUnmounted kotlin.comparisons  invoke kotlin.comparisons  isEmpty kotlin.comparisons  
isInitialized kotlin.comparisons  
isNotEmpty kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  isServiceConnected kotlin.comparisons  java kotlin.comparisons  	javaClass kotlin.comparisons  joinToString kotlin.comparisons  let kotlin.comparisons  	lowercase kotlin.comparisons  mapOf kotlin.comparisons  
mutableListOf kotlin.comparisons  
plusAssign kotlin.comparisons  readText kotlin.comparisons  
s2sService kotlin.comparisons  set kotlin.comparisons  setOf kotlin.comparisons  storageDeviceAdapter kotlin.comparisons  	substring kotlin.comparisons  sumOf kotlin.comparisons  synchronized kotlin.comparisons  to kotlin.comparisons  toList kotlin.comparisons  
toMutableList kotlin.comparisons  toTypedArray kotlin.comparisons  updateNotification kotlin.comparisons  use kotlin.comparisons  	writeText kotlin.comparisons  ACTION_STORAGE_STATUS_CHANGED 	kotlin.io  ActivityMainBinding 	kotlin.io  ActivityResultContracts 	kotlin.io  Bundle 	kotlin.io  	ByteArray 	kotlin.io  
CHANNEL_ID 	kotlin.io  ConcurrentHashMap 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  Date 	kotlin.io  EXTRA_STORAGE_DEVICES 	kotlin.io  EXTRA_USB_CONNECTED 	kotlin.io  EXTRA_USB_PATH 	kotlin.io  Environment 	kotlin.io  	Exception 	kotlin.io  	Executors 	kotlin.io  File 	kotlin.io  FileInputStream 	kotlin.io  FileOutputStream 	kotlin.io  FileProvider 	kotlin.io  
FileWriter 	kotlin.io  Gson 	kotlin.io  IS2SService 	kotlin.io  Intent 	kotlin.io  IntentFilter 	kotlin.io  LayoutInflater 	kotlin.io  LinearLayoutManager 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  LogUtils 	kotlin.io  Manifest 	kotlin.io  
MessageDigest 	kotlin.io  NOTIFICATION_ID 	kotlin.io  NotificationChannel 	kotlin.io  NotificationCompat 	kotlin.io  NotificationManager 	kotlin.io  PackageManager 	kotlin.io  R 	kotlin.io  Regex 	kotlin.io  S2S_SERVICE_ACTION 	kotlin.io  S2S_SERVICE_PACKAGE 	kotlin.io  START_STICKY 	kotlin.io  SimpleDateFormat 	kotlin.io  StorageDeviceAdapter 	kotlin.io  StorageDeviceInfo 	kotlin.io  StorageDeviceViewHolder 	kotlin.io  StorageStatusManager 	kotlin.io  String 	kotlin.io  System 	kotlin.io  TAG 	kotlin.io  Thread 	kotlin.io  Toast 	kotlin.io  UPGRADE_APP_ID 	kotlin.io  UPGRADE_STATUS_SERVICE_HASH 	kotlin.io  UPGRADE_TASK_SERVICE_HASH 	kotlin.io  UUID 	kotlin.io  
UpgradeConfig 	kotlin.io  UpgradePackageAnalyzer 	kotlin.io  UpgradePackageInfo 	kotlin.io  UpgradeTaskInfo 	kotlin.io  UpgradeTaskManager 	kotlin.io  UpgradeTaskPublisher 	kotlin.io  UpgradeTaskStatus 	kotlin.io  Uri 	kotlin.io  UsbDetectionService 	kotlin.io  Volatile 	kotlin.io  all 	kotlin.io  also 	kotlin.io  android 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  arrayOfNulls 	kotlin.io  broadcastStorageStatus 	kotlin.io  contains 	kotlin.io  	emptyList 	kotlin.io  equals 	kotlin.io  	extension 	kotlin.io  filter 	kotlin.io  find 	kotlin.io  forEach 	kotlin.io  format 	kotlin.io  
getUriForFile 	kotlin.io  handleUsbMounted 	kotlin.io  handleUsbUnmounted 	kotlin.io  invoke 	kotlin.io  isEmpty 	kotlin.io  
isInitialized 	kotlin.io  
isNotEmpty 	kotlin.io  
isNullOrEmpty 	kotlin.io  isServiceConnected 	kotlin.io  java 	kotlin.io  	javaClass 	kotlin.io  joinToString 	kotlin.io  let 	kotlin.io  	lowercase 	kotlin.io  mapOf 	kotlin.io  
mutableListOf 	kotlin.io  
plusAssign 	kotlin.io  readText 	kotlin.io  
s2sService 	kotlin.io  set 	kotlin.io  setOf 	kotlin.io  storageDeviceAdapter 	kotlin.io  	substring 	kotlin.io  sumOf 	kotlin.io  synchronized 	kotlin.io  to 	kotlin.io  toList 	kotlin.io  
toMutableList 	kotlin.io  toTypedArray 	kotlin.io  updateNotification 	kotlin.io  use 	kotlin.io  	writeText 	kotlin.io  ACTION_STORAGE_STATUS_CHANGED 
kotlin.jvm  ActivityMainBinding 
kotlin.jvm  ActivityResultContracts 
kotlin.jvm  Bundle 
kotlin.jvm  	ByteArray 
kotlin.jvm  
CHANNEL_ID 
kotlin.jvm  ConcurrentHashMap 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  Date 
kotlin.jvm  EXTRA_STORAGE_DEVICES 
kotlin.jvm  EXTRA_USB_CONNECTED 
kotlin.jvm  EXTRA_USB_PATH 
kotlin.jvm  Environment 
kotlin.jvm  	Exception 
kotlin.jvm  	Executors 
kotlin.jvm  File 
kotlin.jvm  FileInputStream 
kotlin.jvm  FileOutputStream 
kotlin.jvm  FileProvider 
kotlin.jvm  
FileWriter 
kotlin.jvm  Gson 
kotlin.jvm  IS2SService 
kotlin.jvm  Intent 
kotlin.jvm  IntentFilter 
kotlin.jvm  LayoutInflater 
kotlin.jvm  LinearLayoutManager 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  LogUtils 
kotlin.jvm  Manifest 
kotlin.jvm  
MessageDigest 
kotlin.jvm  NOTIFICATION_ID 
kotlin.jvm  NotificationChannel 
kotlin.jvm  NotificationCompat 
kotlin.jvm  NotificationManager 
kotlin.jvm  PackageManager 
kotlin.jvm  R 
kotlin.jvm  Regex 
kotlin.jvm  S2S_SERVICE_ACTION 
kotlin.jvm  S2S_SERVICE_PACKAGE 
kotlin.jvm  START_STICKY 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  StorageDeviceAdapter 
kotlin.jvm  StorageDeviceInfo 
kotlin.jvm  StorageDeviceViewHolder 
kotlin.jvm  StorageStatusManager 
kotlin.jvm  String 
kotlin.jvm  System 
kotlin.jvm  TAG 
kotlin.jvm  Thread 
kotlin.jvm  Toast 
kotlin.jvm  UPGRADE_APP_ID 
kotlin.jvm  UPGRADE_STATUS_SERVICE_HASH 
kotlin.jvm  UPGRADE_TASK_SERVICE_HASH 
kotlin.jvm  UUID 
kotlin.jvm  
UpgradeConfig 
kotlin.jvm  UpgradePackageAnalyzer 
kotlin.jvm  UpgradePackageInfo 
kotlin.jvm  UpgradeTaskInfo 
kotlin.jvm  UpgradeTaskManager 
kotlin.jvm  UpgradeTaskPublisher 
kotlin.jvm  UpgradeTaskStatus 
kotlin.jvm  Uri 
kotlin.jvm  UsbDetectionService 
kotlin.jvm  Volatile 
kotlin.jvm  all 
kotlin.jvm  also 
kotlin.jvm  android 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  arrayOfNulls 
kotlin.jvm  broadcastStorageStatus 
kotlin.jvm  contains 
kotlin.jvm  	emptyList 
kotlin.jvm  equals 
kotlin.jvm  	extension 
kotlin.jvm  filter 
kotlin.jvm  find 
kotlin.jvm  forEach 
kotlin.jvm  format 
kotlin.jvm  
getUriForFile 
kotlin.jvm  handleUsbMounted 
kotlin.jvm  handleUsbUnmounted 
kotlin.jvm  invoke 
kotlin.jvm  isEmpty 
kotlin.jvm  
isInitialized 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  isServiceConnected 
kotlin.jvm  java 
kotlin.jvm  	javaClass 
kotlin.jvm  joinToString 
kotlin.jvm  let 
kotlin.jvm  	lowercase 
kotlin.jvm  mapOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  
plusAssign 
kotlin.jvm  readText 
kotlin.jvm  
s2sService 
kotlin.jvm  set 
kotlin.jvm  setOf 
kotlin.jvm  storageDeviceAdapter 
kotlin.jvm  	substring 
kotlin.jvm  sumOf 
kotlin.jvm  synchronized 
kotlin.jvm  to 
kotlin.jvm  toList 
kotlin.jvm  
toMutableList 
kotlin.jvm  toTypedArray 
kotlin.jvm  updateNotification 
kotlin.jvm  use 
kotlin.jvm  	writeText 
kotlin.jvm  ACTION_STORAGE_STATUS_CHANGED 
kotlin.ranges  ActivityMainBinding 
kotlin.ranges  ActivityResultContracts 
kotlin.ranges  Bundle 
kotlin.ranges  	ByteArray 
kotlin.ranges  
CHANNEL_ID 
kotlin.ranges  ConcurrentHashMap 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  Date 
kotlin.ranges  EXTRA_STORAGE_DEVICES 
kotlin.ranges  EXTRA_USB_CONNECTED 
kotlin.ranges  EXTRA_USB_PATH 
kotlin.ranges  Environment 
kotlin.ranges  	Exception 
kotlin.ranges  	Executors 
kotlin.ranges  File 
kotlin.ranges  FileInputStream 
kotlin.ranges  FileOutputStream 
kotlin.ranges  FileProvider 
kotlin.ranges  
FileWriter 
kotlin.ranges  Gson 
kotlin.ranges  IS2SService 
kotlin.ranges  Intent 
kotlin.ranges  IntentFilter 
kotlin.ranges  LayoutInflater 
kotlin.ranges  LinearLayoutManager 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  LogUtils 
kotlin.ranges  Manifest 
kotlin.ranges  
MessageDigest 
kotlin.ranges  NOTIFICATION_ID 
kotlin.ranges  NotificationChannel 
kotlin.ranges  NotificationCompat 
kotlin.ranges  NotificationManager 
kotlin.ranges  PackageManager 
kotlin.ranges  R 
kotlin.ranges  Regex 
kotlin.ranges  S2S_SERVICE_ACTION 
kotlin.ranges  S2S_SERVICE_PACKAGE 
kotlin.ranges  START_STICKY 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  StorageDeviceAdapter 
kotlin.ranges  StorageDeviceInfo 
kotlin.ranges  StorageDeviceViewHolder 
kotlin.ranges  StorageStatusManager 
kotlin.ranges  String 
kotlin.ranges  System 
kotlin.ranges  TAG 
kotlin.ranges  Thread 
kotlin.ranges  Toast 
kotlin.ranges  UPGRADE_APP_ID 
kotlin.ranges  UPGRADE_STATUS_SERVICE_HASH 
kotlin.ranges  UPGRADE_TASK_SERVICE_HASH 
kotlin.ranges  UUID 
kotlin.ranges  
UpgradeConfig 
kotlin.ranges  UpgradePackageAnalyzer 
kotlin.ranges  UpgradePackageInfo 
kotlin.ranges  UpgradeTaskInfo 
kotlin.ranges  UpgradeTaskManager 
kotlin.ranges  UpgradeTaskPublisher 
kotlin.ranges  UpgradeTaskStatus 
kotlin.ranges  Uri 
kotlin.ranges  UsbDetectionService 
kotlin.ranges  Volatile 
kotlin.ranges  all 
kotlin.ranges  also 
kotlin.ranges  android 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  arrayOfNulls 
kotlin.ranges  broadcastStorageStatus 
kotlin.ranges  contains 
kotlin.ranges  	emptyList 
kotlin.ranges  equals 
kotlin.ranges  	extension 
kotlin.ranges  filter 
kotlin.ranges  find 
kotlin.ranges  forEach 
kotlin.ranges  format 
kotlin.ranges  
getUriForFile 
kotlin.ranges  handleUsbMounted 
kotlin.ranges  handleUsbUnmounted 
kotlin.ranges  invoke 
kotlin.ranges  isEmpty 
kotlin.ranges  
isInitialized 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  isServiceConnected 
kotlin.ranges  java 
kotlin.ranges  	javaClass 
kotlin.ranges  joinToString 
kotlin.ranges  let 
kotlin.ranges  	lowercase 
kotlin.ranges  mapOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  
plusAssign 
kotlin.ranges  readText 
kotlin.ranges  
s2sService 
kotlin.ranges  set 
kotlin.ranges  setOf 
kotlin.ranges  storageDeviceAdapter 
kotlin.ranges  	substring 
kotlin.ranges  sumOf 
kotlin.ranges  synchronized 
kotlin.ranges  to 
kotlin.ranges  toList 
kotlin.ranges  
toMutableList 
kotlin.ranges  toTypedArray 
kotlin.ranges  updateNotification 
kotlin.ranges  use 
kotlin.ranges  	writeText 
kotlin.ranges  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  getISInitialized  kotlin.reflect.KMutableProperty0  getIsInitialized  kotlin.reflect.KMutableProperty0  
isInitialized  kotlin.reflect.KMutableProperty0  ACTION_STORAGE_STATUS_CHANGED kotlin.sequences  ActivityMainBinding kotlin.sequences  ActivityResultContracts kotlin.sequences  Bundle kotlin.sequences  	ByteArray kotlin.sequences  
CHANNEL_ID kotlin.sequences  ConcurrentHashMap kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  Date kotlin.sequences  EXTRA_STORAGE_DEVICES kotlin.sequences  EXTRA_USB_CONNECTED kotlin.sequences  EXTRA_USB_PATH kotlin.sequences  Environment kotlin.sequences  	Exception kotlin.sequences  	Executors kotlin.sequences  File kotlin.sequences  FileInputStream kotlin.sequences  FileOutputStream kotlin.sequences  FileProvider kotlin.sequences  
FileWriter kotlin.sequences  Gson kotlin.sequences  IS2SService kotlin.sequences  Intent kotlin.sequences  IntentFilter kotlin.sequences  LayoutInflater kotlin.sequences  LinearLayoutManager kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  LogUtils kotlin.sequences  Manifest kotlin.sequences  
MessageDigest kotlin.sequences  NOTIFICATION_ID kotlin.sequences  NotificationChannel kotlin.sequences  NotificationCompat kotlin.sequences  NotificationManager kotlin.sequences  PackageManager kotlin.sequences  R kotlin.sequences  Regex kotlin.sequences  S2S_SERVICE_ACTION kotlin.sequences  S2S_SERVICE_PACKAGE kotlin.sequences  START_STICKY kotlin.sequences  SimpleDateFormat kotlin.sequences  StorageDeviceAdapter kotlin.sequences  StorageDeviceInfo kotlin.sequences  StorageDeviceViewHolder kotlin.sequences  StorageStatusManager kotlin.sequences  String kotlin.sequences  System kotlin.sequences  TAG kotlin.sequences  Thread kotlin.sequences  Toast kotlin.sequences  UPGRADE_APP_ID kotlin.sequences  UPGRADE_STATUS_SERVICE_HASH kotlin.sequences  UPGRADE_TASK_SERVICE_HASH kotlin.sequences  UUID kotlin.sequences  
UpgradeConfig kotlin.sequences  UpgradePackageAnalyzer kotlin.sequences  UpgradePackageInfo kotlin.sequences  UpgradeTaskInfo kotlin.sequences  UpgradeTaskManager kotlin.sequences  UpgradeTaskPublisher kotlin.sequences  UpgradeTaskStatus kotlin.sequences  Uri kotlin.sequences  UsbDetectionService kotlin.sequences  Volatile kotlin.sequences  all kotlin.sequences  also kotlin.sequences  android kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  arrayOfNulls kotlin.sequences  broadcastStorageStatus kotlin.sequences  contains kotlin.sequences  	emptyList kotlin.sequences  equals kotlin.sequences  	extension kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  format kotlin.sequences  
getUriForFile kotlin.sequences  handleUsbMounted kotlin.sequences  handleUsbUnmounted kotlin.sequences  invoke kotlin.sequences  isEmpty kotlin.sequences  
isInitialized kotlin.sequences  
isNotEmpty kotlin.sequences  
isNullOrEmpty kotlin.sequences  isServiceConnected kotlin.sequences  java kotlin.sequences  	javaClass kotlin.sequences  joinToString kotlin.sequences  let kotlin.sequences  	lowercase kotlin.sequences  mapOf kotlin.sequences  
mutableListOf kotlin.sequences  
plusAssign kotlin.sequences  readText kotlin.sequences  
s2sService kotlin.sequences  set kotlin.sequences  setOf kotlin.sequences  storageDeviceAdapter kotlin.sequences  	substring kotlin.sequences  sumOf kotlin.sequences  synchronized kotlin.sequences  to kotlin.sequences  toList kotlin.sequences  
toMutableList kotlin.sequences  toTypedArray kotlin.sequences  updateNotification kotlin.sequences  use kotlin.sequences  	writeText kotlin.sequences  ACTION_STORAGE_STATUS_CHANGED kotlin.text  ActivityMainBinding kotlin.text  ActivityResultContracts kotlin.text  Bundle kotlin.text  	ByteArray kotlin.text  
CHANNEL_ID kotlin.text  ConcurrentHashMap kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  Date kotlin.text  EXTRA_STORAGE_DEVICES kotlin.text  EXTRA_USB_CONNECTED kotlin.text  EXTRA_USB_PATH kotlin.text  Environment kotlin.text  	Exception kotlin.text  	Executors kotlin.text  File kotlin.text  FileInputStream kotlin.text  FileOutputStream kotlin.text  FileProvider kotlin.text  
FileWriter kotlin.text  Gson kotlin.text  IS2SService kotlin.text  Intent kotlin.text  IntentFilter kotlin.text  LayoutInflater kotlin.text  LinearLayoutManager kotlin.text  Locale kotlin.text  Log kotlin.text  LogUtils kotlin.text  Manifest kotlin.text  MatchResult kotlin.text  
MessageDigest kotlin.text  NOTIFICATION_ID kotlin.text  NotificationChannel kotlin.text  NotificationCompat kotlin.text  NotificationManager kotlin.text  PackageManager kotlin.text  R kotlin.text  Regex kotlin.text  S2S_SERVICE_ACTION kotlin.text  S2S_SERVICE_PACKAGE kotlin.text  START_STICKY kotlin.text  SimpleDateFormat kotlin.text  StorageDeviceAdapter kotlin.text  StorageDeviceInfo kotlin.text  StorageDeviceViewHolder kotlin.text  StorageStatusManager kotlin.text  String kotlin.text  System kotlin.text  TAG kotlin.text  Thread kotlin.text  Toast kotlin.text  UPGRADE_APP_ID kotlin.text  UPGRADE_STATUS_SERVICE_HASH kotlin.text  UPGRADE_TASK_SERVICE_HASH kotlin.text  UUID kotlin.text  
UpgradeConfig kotlin.text  UpgradePackageAnalyzer kotlin.text  UpgradePackageInfo kotlin.text  UpgradeTaskInfo kotlin.text  UpgradeTaskManager kotlin.text  UpgradeTaskPublisher kotlin.text  UpgradeTaskStatus kotlin.text  Uri kotlin.text  UsbDetectionService kotlin.text  Volatile kotlin.text  all kotlin.text  also kotlin.text  android kotlin.text  apply kotlin.text  arrayOf kotlin.text  arrayOfNulls kotlin.text  broadcastStorageStatus kotlin.text  contains kotlin.text  	emptyList kotlin.text  equals kotlin.text  	extension kotlin.text  filter kotlin.text  find kotlin.text  forEach kotlin.text  format kotlin.text  
getUriForFile kotlin.text  handleUsbMounted kotlin.text  handleUsbUnmounted kotlin.text  invoke kotlin.text  isEmpty kotlin.text  
isInitialized kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  isServiceConnected kotlin.text  java kotlin.text  	javaClass kotlin.text  joinToString kotlin.text  let kotlin.text  	lowercase kotlin.text  mapOf kotlin.text  
mutableListOf kotlin.text  
plusAssign kotlin.text  readText kotlin.text  
s2sService kotlin.text  set kotlin.text  setOf kotlin.text  storageDeviceAdapter kotlin.text  	substring kotlin.text  sumOf kotlin.text  synchronized kotlin.text  to kotlin.text  toList kotlin.text  
toMutableList kotlin.text  toTypedArray kotlin.text  updateNotification kotlin.text  use kotlin.text  	writeText kotlin.text  groupValues kotlin.text.MatchResult  find kotlin.text.Regex  invoke kotlin.text.Regex.Companion  PermissionUtils com.seres.usb.upgrade.utils  READ_MEDIA_AUDIO android.Manifest.permission  READ_MEDIA_IMAGES android.Manifest.permission  READ_MEDIA_VIDEO android.Manifest.permission  AlertDialog android.app.Activity  Build android.app.Activity  Environment android.app.Activity  	Exception android.app.Activity  Intent android.app.Activity  Settings android.app.Activity  String android.app.Activity  Uri android.app.Activity  checkAndroid11Permissions android.app.Activity  checkAndroid13Permissions android.app.Activity  checkLegacyPermissions android.app.Activity  $checkManageExternalStoragePermission android.app.Activity  
mutableListOf android.app.Activity  openAppSettings android.app.Activity  packageName android.app.Activity  &requestManageExternalStoragePermission android.app.Activity  'showManageStoragePermissionDeniedDialog android.app.Activity  !showManageStoragePermissionDialog android.app.Activity  showPermissionDeniedDialog android.app.Activity  
startActivity android.app.Activity  DialogInterface android.content  AlertDialog android.content.Context  Build android.content.Context  Environment android.content.Context  Settings android.content.Context  Uri android.content.Context  checkAndroid11Permissions android.content.Context  checkAndroid13Permissions android.content.Context  checkLegacyPermissions android.content.Context  $checkManageExternalStoragePermission android.content.Context  
mutableListOf android.content.Context  openAppSettings android.content.Context  &requestManageExternalStoragePermission android.content.Context  'showManageStoragePermissionDeniedDialog android.content.Context  !showManageStoragePermissionDialog android.content.Context  showPermissionDeniedDialog android.content.Context  AlertDialog android.content.ContextWrapper  Build android.content.ContextWrapper  Environment android.content.ContextWrapper  Settings android.content.ContextWrapper  Uri android.content.ContextWrapper  checkAndroid11Permissions android.content.ContextWrapper  checkAndroid13Permissions android.content.ContextWrapper  checkLegacyPermissions android.content.ContextWrapper  $checkManageExternalStoragePermission android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  openAppSettings android.content.ContextWrapper  packageName android.content.ContextWrapper  &requestManageExternalStoragePermission android.content.ContextWrapper  'showManageStoragePermissionDeniedDialog android.content.ContextWrapper  !showManageStoragePermissionDialog android.content.ContextWrapper  showPermissionDeniedDialog android.content.ContextWrapper  
startActivity android.content.ContextWrapper  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  getPACKAGEName android.content.Intent  getPackageName android.content.Intent  packageName android.content.Intent  Build 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  Q android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  isExternalStorageManager android.os.Environment  Settings android.provider  #ACTION_APPLICATION_DETAILS_SETTINGS android.provider.Settings  )ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION android.provider.Settings  -ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION android.provider.Settings  AlertDialog  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  Environment  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Settings  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  checkAndroid11Permissions  android.view.ContextThemeWrapper  checkAndroid13Permissions  android.view.ContextThemeWrapper  checkLegacyPermissions  android.view.ContextThemeWrapper  $checkManageExternalStoragePermission  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  openAppSettings  android.view.ContextThemeWrapper  packageName  android.view.ContextThemeWrapper  &requestManageExternalStoragePermission  android.view.ContextThemeWrapper  'showManageStoragePermissionDeniedDialog  android.view.ContextThemeWrapper  !showManageStoragePermissionDialog  android.view.ContextThemeWrapper  showPermissionDeniedDialog  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  AlertDialog #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  Environment #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  Settings #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  checkAndroid11Permissions #androidx.activity.ComponentActivity  checkAndroid13Permissions #androidx.activity.ComponentActivity  checkLegacyPermissions #androidx.activity.ComponentActivity  $checkManageExternalStoragePermission #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  openAppSettings #androidx.activity.ComponentActivity  packageName #androidx.activity.ComponentActivity  &requestManageExternalStoragePermission #androidx.activity.ComponentActivity  'showManageStoragePermissionDeniedDialog #androidx.activity.ComponentActivity  !showManageStoragePermissionDialog #androidx.activity.ComponentActivity  showPermissionDeniedDialog #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  ActivityResult androidx.activity.result  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  invoke Zandroidx.activity.result.contract.ActivityResultContracts.StartActivityForResult.Companion  AlertDialog androidx.appcompat.app  Builder "androidx.appcompat.app.AlertDialog  
setCancelable *androidx.appcompat.app.AlertDialog.Builder  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  AlertDialog (androidx.appcompat.app.AppCompatActivity  Build (androidx.appcompat.app.AppCompatActivity  Environment (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  Settings (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  Uri (androidx.appcompat.app.AppCompatActivity  checkAndroid11Permissions (androidx.appcompat.app.AppCompatActivity  checkAndroid13Permissions (androidx.appcompat.app.AppCompatActivity  checkLegacyPermissions (androidx.appcompat.app.AppCompatActivity  $checkManageExternalStoragePermission (androidx.appcompat.app.AppCompatActivity  
mutableListOf (androidx.appcompat.app.AppCompatActivity  openAppSettings (androidx.appcompat.app.AppCompatActivity  packageName (androidx.appcompat.app.AppCompatActivity  &requestManageExternalStoragePermission (androidx.appcompat.app.AppCompatActivity  'showManageStoragePermissionDeniedDialog (androidx.appcompat.app.AppCompatActivity  !showManageStoragePermissionDialog (androidx.appcompat.app.AppCompatActivity  showPermissionDeniedDialog (androidx.appcompat.app.AppCompatActivity  
startActivity (androidx.appcompat.app.AppCompatActivity  AlertDialog #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Environment #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  Settings #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  checkAndroid11Permissions #androidx.core.app.ComponentActivity  checkAndroid13Permissions #androidx.core.app.ComponentActivity  checkLegacyPermissions #androidx.core.app.ComponentActivity  $checkManageExternalStoragePermission #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  openAppSettings #androidx.core.app.ComponentActivity  packageName #androidx.core.app.ComponentActivity  &requestManageExternalStoragePermission #androidx.core.app.ComponentActivity  'showManageStoragePermissionDeniedDialog #androidx.core.app.ComponentActivity  !showManageStoragePermissionDialog #androidx.core.app.ComponentActivity  showPermissionDeniedDialog #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  AlertDialog &androidx.fragment.app.FragmentActivity  Build &androidx.fragment.app.FragmentActivity  Environment &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  Settings &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  Uri &androidx.fragment.app.FragmentActivity  checkAndroid11Permissions &androidx.fragment.app.FragmentActivity  checkAndroid13Permissions &androidx.fragment.app.FragmentActivity  checkLegacyPermissions &androidx.fragment.app.FragmentActivity  $checkManageExternalStoragePermission &androidx.fragment.app.FragmentActivity  
mutableListOf &androidx.fragment.app.FragmentActivity  openAppSettings &androidx.fragment.app.FragmentActivity  packageName &androidx.fragment.app.FragmentActivity  &requestManageExternalStoragePermission &androidx.fragment.app.FragmentActivity  'showManageStoragePermissionDeniedDialog &androidx.fragment.app.FragmentActivity  !showManageStoragePermissionDialog &androidx.fragment.app.FragmentActivity  showPermissionDeniedDialog &androidx.fragment.app.FragmentActivity  
startActivity &androidx.fragment.app.FragmentActivity  AlertDialog com.seres.usb.upgrade  Build com.seres.usb.upgrade  Environment com.seres.usb.upgrade  Settings com.seres.usb.upgrade  String com.seres.usb.upgrade  Uri com.seres.usb.upgrade  
mutableListOf com.seres.usb.upgrade  packageName com.seres.usb.upgrade  AlertDialog "com.seres.usb.upgrade.MainActivity  Build "com.seres.usb.upgrade.MainActivity  Environment "com.seres.usb.upgrade.MainActivity  	Exception "com.seres.usb.upgrade.MainActivity  Intent "com.seres.usb.upgrade.MainActivity  Settings "com.seres.usb.upgrade.MainActivity  String "com.seres.usb.upgrade.MainActivity  Uri "com.seres.usb.upgrade.MainActivity  checkAndroid11Permissions "com.seres.usb.upgrade.MainActivity  checkAndroid13Permissions "com.seres.usb.upgrade.MainActivity  checkLegacyPermissions "com.seres.usb.upgrade.MainActivity  $checkManageExternalStoragePermission "com.seres.usb.upgrade.MainActivity  getMUTABLEListOf "com.seres.usb.upgrade.MainActivity  getMutableListOf "com.seres.usb.upgrade.MainActivity  getPACKAGEName "com.seres.usb.upgrade.MainActivity  getPackageName "com.seres.usb.upgrade.MainActivity  manageExternalStorageLauncher "com.seres.usb.upgrade.MainActivity  
mutableListOf "com.seres.usb.upgrade.MainActivity  openAppSettings "com.seres.usb.upgrade.MainActivity  packageName "com.seres.usb.upgrade.MainActivity  &requestManageExternalStoragePermission "com.seres.usb.upgrade.MainActivity  setPackageName "com.seres.usb.upgrade.MainActivity  'showManageStoragePermissionDeniedDialog "com.seres.usb.upgrade.MainActivity  !showManageStoragePermissionDialog "com.seres.usb.upgrade.MainActivity  showPermissionDeniedDialog "com.seres.usb.upgrade.MainActivity  
startActivity "com.seres.usb.upgrade.MainActivity  AlertDialog ,com.seres.usb.upgrade.MainActivity.Companion  Build ,com.seres.usb.upgrade.MainActivity.Companion  Environment ,com.seres.usb.upgrade.MainActivity.Companion  	Exception ,com.seres.usb.upgrade.MainActivity.Companion  Intent ,com.seres.usb.upgrade.MainActivity.Companion  Settings ,com.seres.usb.upgrade.MainActivity.Companion  String ,com.seres.usb.upgrade.MainActivity.Companion  Uri ,com.seres.usb.upgrade.MainActivity.Companion  getMUTABLEListOf ,com.seres.usb.upgrade.MainActivity.Companion  getMutableListOf ,com.seres.usb.upgrade.MainActivity.Companion  
mutableListOf ,com.seres.usb.upgrade.MainActivity.Companion  packageName ,com.seres.usb.upgrade.MainActivity.Companion  PermissionUtils com.seres.usb.upgrade.storage  PermissionUtils 2com.seres.usb.upgrade.storage.StorageStatusManager  Build com.seres.usb.upgrade.utils  
ContextCompat com.seres.usb.upgrade.utils  Environment com.seres.usb.upgrade.utils  List com.seres.usb.upgrade.utils  Manifest com.seres.usb.upgrade.utils  PackageManager com.seres.usb.upgrade.utils  
mutableListOf com.seres.usb.upgrade.utils  Boolean +com.seres.usb.upgrade.utils.PermissionUtils  Build +com.seres.usb.upgrade.utils.PermissionUtils  Context +com.seres.usb.upgrade.utils.PermissionUtils  
ContextCompat +com.seres.usb.upgrade.utils.PermissionUtils  Environment +com.seres.usb.upgrade.utils.PermissionUtils  List +com.seres.usb.upgrade.utils.PermissionUtils  Manifest +com.seres.usb.upgrade.utils.PermissionUtils  PackageManager +com.seres.usb.upgrade.utils.PermissionUtils  String +com.seres.usb.upgrade.utils.PermissionUtils  getMUTABLEListOf +com.seres.usb.upgrade.utils.PermissionUtils  getMutableListOf +com.seres.usb.upgrade.utils.PermissionUtils  hasLegacyStoragePermissions +com.seres.usb.upgrade.utils.PermissionUtils  hasMediaPermissions +com.seres.usb.upgrade.utils.PermissionUtils  hasStoragePermission +com.seres.usb.upgrade.utils.PermissionUtils  
mutableListOf +com.seres.usb.upgrade.utils.PermissionUtils  AlertDialog 	java.lang  Build 	java.lang  PermissionUtils 	java.lang  Settings 	java.lang  packageName 	java.lang  AlertDialog kotlin  Build kotlin  	Function2 kotlin  PermissionUtils kotlin  Settings kotlin  packageName kotlin  AlertDialog kotlin.annotation  Build kotlin.annotation  PermissionUtils kotlin.annotation  Settings kotlin.annotation  packageName kotlin.annotation  AlertDialog kotlin.collections  Build kotlin.collections  PermissionUtils kotlin.collections  Settings kotlin.collections  packageName kotlin.collections  getTOTypedArray kotlin.collections.MutableList  getToTypedArray kotlin.collections.MutableList  AlertDialog kotlin.comparisons  Build kotlin.comparisons  PermissionUtils kotlin.comparisons  Settings kotlin.comparisons  packageName kotlin.comparisons  AlertDialog 	kotlin.io  Build 	kotlin.io  PermissionUtils 	kotlin.io  Settings 	kotlin.io  packageName 	kotlin.io  AlertDialog 
kotlin.jvm  Build 
kotlin.jvm  PermissionUtils 
kotlin.jvm  Settings 
kotlin.jvm  packageName 
kotlin.jvm  AlertDialog 
kotlin.ranges  Build 
kotlin.ranges  PermissionUtils 
kotlin.ranges  Settings 
kotlin.ranges  packageName 
kotlin.ranges  AlertDialog kotlin.sequences  Build kotlin.sequences  PermissionUtils kotlin.sequences  Settings kotlin.sequences  packageName kotlin.sequences  AlertDialog kotlin.text  Build kotlin.text  PermissionUtils kotlin.text  Settings kotlin.text  packageName kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              