android.os.Parcelablejava.io.Serializableandroid.os.Parcelable.Creatorandroid.app.Serviceandroid.os.IInterfaceandroid.os.Binder(androidx.appcompat.app.AppCompatActivityandroid.app.Application1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderkotlin.Enum!android.content.BroadcastReceiver/com.seres.usb.upgrade.aidl.IAsyncResultCallback androidx.viewbinding.ViewBinding&com.seres.usb.upgrade.aidl.IS2SService                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             