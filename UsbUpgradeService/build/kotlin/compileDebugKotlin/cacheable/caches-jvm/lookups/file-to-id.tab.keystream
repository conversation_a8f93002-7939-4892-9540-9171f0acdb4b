EUsbUpgradeService/src/main/java/com/seres/usb/upgrade/MainActivity.ktNUsbUpgradeService/src/main/java/com/seres/usb/upgrade/UsbUpgradeApplication.ktUUsbUpgradeService/src/main/java/com/seres/usb/upgrade/adapter/StorageDeviceAdapter.ktXUsbUpgradeService/src/main/java/com/seres/usb/upgrade/analyzer/UpgradePackageAnalyzer.ktSUsbUpgradeService/src/main/java/com/seres/usb/upgrade/manager/UpgradeTaskManager.ktQUsbUpgradeService/src/main/java/com/seres/usb/upgrade/model/UpgradePackageInfo.ktNUsbUpgradeService/src/main/java/com/seres/usb/upgrade/model/UpgradeTaskInfo.ktWUsbUpgradeService/src/main/java/com/seres/usb/upgrade/publisher/UpgradeTaskPublisher.ktVUsbUpgradeService/src/main/java/com/seres/usb/upgrade/receiver/BootCompleteReceiver.ktSUsbUpgradeService/src/main/java/com/seres/usb/upgrade/service/UpgradeTaskService.ktTUsbUpgradeService/src/main/java/com/seres/usb/upgrade/service/UsbDetectionService.ktUUsbUpgradeService/src/main/java/com/seres/usb/upgrade/storage/StorageStatusManager.ktGUsbUpgradeService/src/main/java/com/seres/usb/upgrade/utils/LogUtils.ktNUsbUpgradeService/src/main/java/com/seres/usb/upgrade/utils/PermissionUtils.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  