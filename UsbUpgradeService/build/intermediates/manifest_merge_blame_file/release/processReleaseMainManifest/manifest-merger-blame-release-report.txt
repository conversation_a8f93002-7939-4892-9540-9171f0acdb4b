1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.seres.usb.upgrade"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="31"
9        android:targetSdkVersion="34" />
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
12-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:6:5-81
12-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:7:5-77
13-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
14-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:8:5-87
14-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:8:22-84
15
16    <!-- 存储权限 -->
17    <uses-permission
17-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:11:22-77
19        android:maxSdkVersion="32" />
19-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:12:9-35
20    <uses-permission
20-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:13:5-14:38
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:13:22-78
22        android:maxSdkVersion="29" />
22-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:14:9-35
23    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
23-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:15:5-16:40
23-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:15:22-79
24
25    <!-- Android 13+ 权限 -->
26    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
26-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:19:5-76
26-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:19:22-73
27    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
27-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:20:5-75
27-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:20:22-72
28    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
28-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:21:5-75
28-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:21:22-72
29
30    <!-- USB和其他权限 -->
31    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
31-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:24:5-25:47
31-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:24:22-81
32    <uses-permission android:name="android.hardware.usb.host" />
32-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:26:5-65
32-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:26:22-62
33    <uses-permission android:name="android.permission.INTERNET" />
33-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:27:5-67
33-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:27:22-64
34
35    <!-- USB设备访问 -->
36    <uses-feature
36-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:30:5-87
37        android:name="android.hardware.usb.host"
37-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:30:19-59
38        android:required="false" />
38-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:30:60-84
39
40    <permission
40-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
41        android:name="com.seres.usb.upgrade.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.seres.usb.upgrade.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
45
46    <application
46-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:32:5-96:19
47        android:name="com.seres.usb.upgrade.UsbUpgradeApplication"
47-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:33:9-46
48        android:allowBackup="true"
48-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:34:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
50        android:dataExtractionRules="@xml/data_extraction_rules"
50-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:35:9-65
51        android:extractNativeLibs="false"
52        android:fullBackupContent="@xml/backup_rules"
52-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:36:9-54
53        android:icon="@mipmap/ic_launcher"
53-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:37:9-43
54        android:label="@string/app_name"
54-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:38:9-41
55        android:requestLegacyExternalStorage="true"
55-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:42:9-52
56        android:roundIcon="@mipmap/ic_launcher_round"
56-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:39:9-54
57        android:supportsRtl="true"
57-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:40:9-35
58        android:theme="@style/Theme.UsbUpgradeService" >
58-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:41:9-55
59
60        <!-- 主Activity -->
61        <activity
61-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:46:9-54:20
62            android:name="com.seres.usb.upgrade.MainActivity"
62-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:47:13-41
63            android:exported="true"
63-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:48:13-36
64            android:theme="@style/Theme.UsbUpgradeService" >
64-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:49:13-59
65            <intent-filter>
65-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:50:13-53:29
66                <action android:name="android.intent.action.MAIN" />
66-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:51:17-69
66-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:51:25-66
67
68                <category android:name="android.intent.category.LAUNCHER" />
68-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:52:17-77
68-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:52:27-74
69            </intent-filter>
70        </activity>
71
72        <!-- USB检测服务 -->
73        <service
73-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:57:9-61:56
74            android:name="com.seres.usb.upgrade.service.UsbDetectionService"
74-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:58:13-56
75            android:enabled="true"
75-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:59:13-35
76            android:exported="false"
76-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:60:13-37
77            android:foregroundServiceType="dataSync" />
77-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:61:13-53
78
79        <!-- 升级任务服务 -->
80        <service
80-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:64:9-71:19
81            android:name="com.seres.usb.upgrade.service.UpgradeTaskService"
81-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:65:13-55
82            android:enabled="true"
82-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:66:13-35
83            android:exported="true" >
83-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:67:13-36
84            <intent-filter>
84-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:68:13-70:29
85                <action android:name="com.seres.usb.upgrade.action.UPGRADE_SERVICE" />
85-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:69:17-87
85-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:69:25-84
86            </intent-filter>
87        </service>
88
89        <!-- 开机启动接收器 -->
90        <receiver
90-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:74:9-83:20
91            android:name="com.seres.usb.upgrade.receiver.BootCompleteReceiver"
91-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:75:13-58
92            android:enabled="true"
92-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:76:13-35
93            android:exported="true" >
93-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:77:13-36
94            <intent-filter android:priority="1000" >
94-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:78:13-82:29
94-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:78:28-51
95                <action android:name="android.intent.action.BOOT_COMPLETED" />
95-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:79:17-79
95-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:79:25-76
96                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
96-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:80:17-82
96-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:80:25-79
97
98                <category android:name="android.intent.category.DEFAULT" />
98-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:81:17-76
98-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:81:27-73
99            </intent-filter>
100        </receiver>
101
102        <!-- FileProvider -->
103        <provider
104            android:name="androidx.core.content.FileProvider"
104-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:87:13-62
105            android:authorities="com.seres.usb.upgrade.fileprovider"
105-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:88:13-64
106            android:exported="false"
106-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:89:13-37
107            android:grantUriPermissions="true" >
107-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:90:13-47
108            <meta-data
108-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:91:13-93:54
109                android:name="android.support.FILE_PROVIDER_PATHS"
109-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:92:17-67
110                android:resource="@xml/file_paths" />
110-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:93:17-51
111        </provider>
112        <provider
112-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
113            android:name="androidx.startup.InitializationProvider"
113-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
114            android:authorities="com.seres.usb.upgrade.androidx-startup"
114-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
115            android:exported="false" >
115-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
116            <meta-data
116-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
117                android:name="androidx.emoji2.text.EmojiCompatInitializer"
117-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
118                android:value="androidx.startup" />
118-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
119            <meta-data
119-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:29:13-31:52
120                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
120-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:30:17-78
121                android:value="androidx.startup" />
121-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:31:17-49
122            <meta-data
122-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
123                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
123-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
124                android:value="androidx.startup" />
124-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
125        </provider>
126
127        <receiver
127-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
128            android:name="androidx.profileinstaller.ProfileInstallReceiver"
128-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
129            android:directBootAware="false"
129-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
130            android:enabled="true"
130-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
131            android:exported="true"
131-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
132            android:permission="android.permission.DUMP" >
132-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
134                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
134-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
134-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
137                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
137-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
137-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
138            </intent-filter>
139            <intent-filter>
139-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
140                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
140-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
140-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
141            </intent-filter>
142            <intent-filter>
142-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
143                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
143-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
143-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
144            </intent-filter>
145        </receiver>
146    </application>
147
148</manifest>
