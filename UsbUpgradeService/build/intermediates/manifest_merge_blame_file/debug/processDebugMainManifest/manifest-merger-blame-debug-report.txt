1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.seres.usb.upgrade"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="31"
9        android:targetSdkVersion="34" />
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
12-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:6:5-81
12-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:7:5-77
13-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
14-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:8:5-87
14-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:8:22-84
15
16    <!-- 存储权限 -->
17    <uses-permission
17-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:11:22-77
19        android:maxSdkVersion="32" />
19-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:12:9-35
20    <uses-permission
20-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:13:5-14:38
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:13:22-78
22        android:maxSdkVersion="29" />
22-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:14:9-35
23    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
23-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:15:5-16:40
23-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:15:22-79
24
25    <!-- Android 13+ 权限 -->
26    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
26-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:19:5-76
26-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:19:22-73
27    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
27-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:20:5-75
27-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:20:22-72
28    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
28-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:21:5-75
28-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:21:22-72
29
30    <!-- USB和其他权限 -->
31    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
31-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:24:5-25:47
31-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:24:22-81
32    <uses-permission android:name="android.hardware.usb.host" />
32-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:26:5-65
32-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:26:22-62
33    <uses-permission android:name="android.permission.INTERNET" />
33-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:27:5-67
33-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:27:22-64
34
35    <!-- USB设备访问 -->
36    <uses-feature
36-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:30:5-87
37        android:name="android.hardware.usb.host"
37-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:30:19-59
38        android:required="false" />
38-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:30:60-84
39
40    <permission
40-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
41        android:name="com.seres.usb.upgrade.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.seres.usb.upgrade.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
45
46    <application
46-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:32:5-96:19
47        android:name="com.seres.usb.upgrade.UsbUpgradeApplication"
47-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:33:9-46
48        android:allowBackup="true"
48-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:34:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.10.1] /home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
50        android:dataExtractionRules="@xml/data_extraction_rules"
50-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:35:9-65
51        android:debuggable="true"
52        android:extractNativeLibs="false"
53        android:fullBackupContent="@xml/backup_rules"
53-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:36:9-54
54        android:icon="@mipmap/ic_launcher"
54-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:37:9-43
55        android:label="@string/app_name"
55-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:38:9-41
56        android:requestLegacyExternalStorage="true"
56-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:42:9-52
57        android:roundIcon="@mipmap/ic_launcher_round"
57-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:39:9-54
58        android:supportsRtl="true"
58-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:40:9-35
59        android:theme="@style/Theme.UsbUpgradeService" >
59-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:41:9-55
60
61        <!-- 主Activity -->
62        <activity
62-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:46:9-54:20
63            android:name="com.seres.usb.upgrade.MainActivity"
63-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:47:13-41
64            android:exported="true"
64-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:48:13-36
65            android:theme="@style/Theme.UsbUpgradeService" >
65-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:49:13-59
66            <intent-filter>
66-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:50:13-53:29
67                <action android:name="android.intent.action.MAIN" />
67-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:51:17-69
67-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:51:25-66
68
69                <category android:name="android.intent.category.LAUNCHER" />
69-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:52:17-77
69-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:52:27-74
70            </intent-filter>
71        </activity>
72
73        <!-- USB检测服务 -->
74        <service
74-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:57:9-61:56
75            android:name="com.seres.usb.upgrade.service.UsbDetectionService"
75-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:58:13-56
76            android:enabled="true"
76-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:59:13-35
77            android:exported="false"
77-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:60:13-37
78            android:foregroundServiceType="dataSync" />
78-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:61:13-53
79
80        <!-- 升级任务服务 -->
81        <service
81-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:64:9-71:19
82            android:name="com.seres.usb.upgrade.service.UpgradeTaskService"
82-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:65:13-55
83            android:enabled="true"
83-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:66:13-35
84            android:exported="true" >
84-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:67:13-36
85            <intent-filter>
85-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:68:13-70:29
86                <action android:name="com.seres.usb.upgrade.action.UPGRADE_SERVICE" />
86-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:69:17-87
86-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:69:25-84
87            </intent-filter>
88        </service>
89
90        <!-- 开机启动接收器 -->
91        <receiver
91-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:74:9-83:20
92            android:name="com.seres.usb.upgrade.receiver.BootCompleteReceiver"
92-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:75:13-58
93            android:enabled="true"
93-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:76:13-35
94            android:exported="true" >
94-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:77:13-36
95            <intent-filter android:priority="1000" >
95-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:78:13-82:29
95-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:78:28-51
96                <action android:name="android.intent.action.BOOT_COMPLETED" />
96-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:79:17-79
96-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:79:25-76
97                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
97-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:80:17-82
97-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:80:25-79
98
99                <category android:name="android.intent.category.DEFAULT" />
99-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:81:17-76
99-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:81:27-73
100            </intent-filter>
101        </receiver>
102
103        <!-- FileProvider -->
104        <provider
105            android:name="androidx.core.content.FileProvider"
105-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:87:13-62
106            android:authorities="com.seres.usb.upgrade.fileprovider"
106-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:88:13-64
107            android:exported="false"
107-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:89:13-37
108            android:grantUriPermissions="true" >
108-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:90:13-47
109            <meta-data
109-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:91:13-93:54
110                android:name="android.support.FILE_PROVIDER_PATHS"
110-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:92:17-67
111                android:resource="@xml/file_paths" />
111-->/home/<USER>/dds/s2sService/UsbUpgradeService/src/main/AndroidManifest.xml:93:17-51
112        </provider>
113        <provider
113-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
114            android:name="androidx.startup.InitializationProvider"
114-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
115            android:authorities="com.seres.usb.upgrade.androidx-startup"
115-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
116            android:exported="false" >
116-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
117            <meta-data
117-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
118                android:name="androidx.emoji2.text.EmojiCompatInitializer"
118-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
119                android:value="androidx.startup" />
119-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/8.9/transforms/f285c1b069f73eebd1998bbe0c6bfcd1/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
120            <meta-data
120-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:29:13-31:52
121                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
121-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:30:17-78
122                android:value="androidx.startup" />
122-->[androidx.lifecycle:lifecycle-process:2.6.1] /home/<USER>/.gradle/caches/8.9/transforms/9ce3b1e16b2aa5eaaa6471fcd2664a57/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:31:17-49
123            <meta-data
123-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
124                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
124-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
125                android:value="androidx.startup" />
125-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
126        </provider>
127
128        <receiver
128-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
129            android:name="androidx.profileinstaller.ProfileInstallReceiver"
129-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
130            android:directBootAware="false"
130-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
131            android:enabled="true"
131-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
132            android:exported="true"
132-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
133            android:permission="android.permission.DUMP" >
133-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
134            <intent-filter>
134-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
135                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
135-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
135-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
136            </intent-filter>
137            <intent-filter>
137-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
138                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
138-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
138-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
139            </intent-filter>
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
141                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
141-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
141-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
142            </intent-filter>
143            <intent-filter>
143-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
144                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
144-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
144-->[androidx.profileinstaller:profileinstaller:1.3.0] /home/<USER>/.gradle/caches/8.9/transforms/2bb4f5cb3832e051b2b51fb3bde898e8/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
145            </intent-filter>
146        </receiver>
147    </application>
148
149</manifest>
