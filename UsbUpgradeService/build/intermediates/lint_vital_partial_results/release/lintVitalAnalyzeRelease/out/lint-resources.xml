http://schemas.android.com/apk/res-auto;;${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_background.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_usb.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_background.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/drawable/section_background.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/layout/item_storage_device.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher_round.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/logo.png,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:UsbUpgradeService*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:white,0,V400030061,**********,;"#FFFFFFFF";black,0,V400020037,290002005c,;"#FF000000";+drawable:button_background,1,F;ic_launcher_foreground,2,F;ic_usb,3,F;status_background,4,F;ic_launcher_background,5,F;section_background,6,F;+id:btnRefreshStorage,7,F;tvTaskCount,7,F;btnStopService,7,F;tvUsbStatus,7,F;tvStorageStatus,7,F;btnOpenDevice,8,F;tvDeviceName,8,F;progressBarUsage,8,F;btnOpenUsb,7,F;tvUsagePercentage,8,F;btnShowActiveTasks,7,F;tvDevicePath,8,F;tvStorageInfo,8,F;btnStartService,7,F;tvDeviceStatus,8,F;tvServiceStatus,7,F;recyclerViewStorageDevices,7,F;btnClearLogs,7,F;+layout:item_storage_device,8,F;activity_main,7,F;+mipmap:ic_launcher_round,9,F;ic_launcher_round,10,F;ic_launcher_round,11,F;ic_launcher_round,12,F;ic_launcher_round,13,F;ic_launcher_round,14,F;logo,15,F;ic_launcher,16,F;ic_launcher,17,F;ic_launcher,18,F;ic_launcher,19,F;ic_launcher,20,F;ic_launcher,21,F;+string:app_name,22,V400020037,2c0002005f,;"USB升级服务";+style:Theme.UsbUpgradeService,23,V40007014c,520007019a,;DBase.Theme.UsbUpgradeService,;Base.Theme.UsbUpgradeService,23,V400020064,c00050146,;DTheme.Material3.DayNight.NoActionBar,;Base.Theme.UsbUpgradeService,24,V400020064,c00050144,;DTheme.Material3.DayNight.NoActionBar,;+xml:file_paths,25,F;data_extraction_rules,26,F;backup_rules,27,F;