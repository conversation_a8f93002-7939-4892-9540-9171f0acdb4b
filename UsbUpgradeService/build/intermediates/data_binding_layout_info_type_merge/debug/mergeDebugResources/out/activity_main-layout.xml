<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.seres.usb.upgrade" filePath="UsbUpgradeService/src/main/res/layout/activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_main_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="195" endOffset="12"/></Target><Target id="@+id/tvServiceStatus" view="TextView"><Expressions/><location startLine="39" startOffset="12" endLine="46" endOffset="52"/></Target><Target id="@+id/btnStartService" view="Button"><Expressions/><location startLine="53" startOffset="16" endLine="59" endOffset="52"/></Target><Target id="@+id/btnStopService" view="Button"><Expressions/><location startLine="61" startOffset="16" endLine="67" endOffset="54"/></Target><Target id="@+id/tvStorageStatus" view="TextView"><Expressions/><location startLine="90" startOffset="12" endLine="96" endOffset="51"/></Target><Target id="@+id/tvUsbStatus" view="TextView"><Expressions/><location startLine="106" startOffset="16" endLine="114" endOffset="70"/></Target><Target id="@+id/btnOpenUsb" view="Button"><Expressions/><location startLine="116" startOffset="16" endLine="122" endOffset="45"/></Target><Target id="@+id/btnRefreshStorage" view="Button"><Expressions/><location startLine="126" startOffset="12" endLine="131" endOffset="52"/></Target><Target id="@+id/recyclerViewStorageDevices" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="134" startOffset="12" endLine="139" endOffset="47"/></Target><Target id="@+id/tvTaskCount" view="TextView"><Expressions/><location startLine="160" startOffset="12" endLine="166" endOffset="52"/></Target><Target id="@+id/btnShowActiveTasks" view="Button"><Expressions/><location startLine="173" startOffset="16" endLine="179" endOffset="52"/></Target><Target id="@+id/btnClearLogs" view="Button"><Expressions/><location startLine="181" startOffset="16" endLine="187" endOffset="54"/></Target></Targets></Layout>