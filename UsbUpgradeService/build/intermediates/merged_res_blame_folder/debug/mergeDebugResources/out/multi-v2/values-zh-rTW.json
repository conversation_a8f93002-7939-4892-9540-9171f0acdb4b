{"logs": [{"outputFile": "com.seres.usb.upgrade.UsbUpgradeService-mergeDebugResources-30:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "/home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3203,3295,3394,3488,3582,3675,3768,8365", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3290,3389,3483,3577,3670,3763,3859,8461"}}, {"source": "/home/<USER>/.gradle/caches/8.9/transforms/16178b8cf6033f49fe8192ba1281dfae/transformed/appcompat-1.6.1/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,8286", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,8360"}}, {"source": "/home/<USER>/.gradle/caches/8.9/transforms/2816070cc58ee779d6eda324d2c0a6ce/transformed/material-1.10.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2452,2529,2603,2653,2704,2770,2833,2901,2979,3050,3111,3182,3249,3311,3398,3477,3542,3625,3710,3784,3848,3924,3972,4045,4109,4185,4263,4325,4389,4452,4518,4598,4676,4752,4831,4885,4940", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2447,2524,2598,2648,2699,2765,2828,2896,2974,3045,3106,3177,3244,3306,3393,3472,3537,3620,3705,3779,3843,3919,3967,4040,4104,4180,4258,4320,4384,4447,4513,4593,4671,4747,4826,4880,4935,5004"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2863,2927,2989,3056,3126,3864,3958,4065,4138,4200,4278,4338,4398,4476,4537,4595,4651,4711,4769,4823,4908,4964,5022,5076,5141,5233,5307,5384,5504,5567,5630,5729,5806,5880,5930,5981,6047,6110,6178,6256,6327,6388,6459,6526,6588,6675,6754,6819,6902,6987,7061,7125,7201,7249,7322,7386,7462,7540,7602,7666,7729,7795,7875,7953,8029,8108,8162,8217", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,2922,2984,3051,3121,3198,3953,4060,4133,4195,4273,4333,4393,4471,4532,4590,4646,4706,4764,4818,4903,4959,5017,5071,5136,5228,5302,5379,5499,5562,5625,5724,5801,5875,5925,5976,6042,6105,6173,6251,6322,6383,6454,6521,6583,6670,6749,6814,6897,6982,7056,7120,7196,7244,7317,7381,7457,7535,7597,7661,7724,7790,7870,7948,8024,8103,8157,8212,8281"}}]}]}