{"logs": [{"outputFile": "com.seres.usb.upgrade.UsbUpgradeService-mergeDebugResources-30:/values-sk/values-sk.xml", "map": [{"source": "/home/<USER>/.gradle/caches/8.9/transforms/f8604fe2bdf93b68a9d0729cd6c4e049/transformed/core-1.10.1/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "40,41,42,43,44,45,46,111", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3543,3639,3741,3842,3940,4050,4158,9412", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3634,3736,3837,3935,4045,4153,4275,9508"}}, {"source": "/home/<USER>/.gradle/caches/8.9/transforms/2816070cc58ee779d6eda324d2c0a6ce/transformed/material-1.10.0/res/values-sk/values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1147,1246,1322,1387,1477,1541,1607,1661,1730,1790,1844,1961,2021,2083,2137,2209,2339,2426,2518,2657,2726,2804,2935,3023,3103,3157,3208,3274,3346,3423,3506,3588,3660,3737,3810,3881,3986,4074,4146,4238,4334,4408,4482,4578,4630,4712,4779,4866,4953,5015,5079,5142,5210,5316,5423,5521,5638,5696,5751", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,130,87,79,53,50,65,71,76,82,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78", "endOffsets": "373,448,523,601,693,776,868,996,1077,1142,1241,1317,1382,1472,1536,1602,1656,1725,1785,1839,1956,2016,2078,2132,2204,2334,2421,2513,2652,2721,2799,2930,3018,3098,3152,3203,3269,3341,3418,3501,3583,3655,3732,3805,3876,3981,4069,4141,4233,4329,4403,4477,4573,4625,4707,4774,4861,4948,5010,5074,5137,5205,5311,5418,5516,5633,5691,5746,5825"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3140,3215,3290,3368,3460,4280,4372,4500,4581,4646,4745,4821,4886,4976,5040,5106,5160,5229,5289,5343,5460,5520,5582,5636,5708,5838,5925,6017,6156,6225,6303,6434,6522,6602,6656,6707,6773,6845,6922,7005,7087,7159,7236,7309,7380,7485,7573,7645,7737,7833,7907,7981,8077,8129,8211,8278,8365,8452,8514,8578,8641,8709,8815,8922,9020,9137,9195,9250", "endLines": "7,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,130,87,79,53,50,65,71,76,82,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78", "endOffsets": "423,3210,3285,3363,3455,3538,4367,4495,4576,4641,4740,4816,4881,4971,5035,5101,5155,5224,5284,5338,5455,5515,5577,5631,5703,5833,5920,6012,6151,6220,6298,6429,6517,6597,6651,6702,6768,6840,6917,7000,7082,7154,7231,7304,7375,7480,7568,7640,7732,7828,7902,7976,8072,8124,8206,8273,8360,8447,8509,8573,8636,8704,8810,8917,9015,9132,9190,9245,9324"}}, {"source": "/home/<USER>/.gradle/caches/8.9/transforms/16178b8cf6033f49fe8192ba1281dfae/transformed/appcompat-1.6.1/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "428,535,636,747,833,941,1059,1138,1215,1306,1399,1497,1591,1691,1784,1879,1977,2068,2159,2243,2348,2456,2555,2661,2773,2876,3042,9329", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "530,631,742,828,936,1054,1133,1210,1301,1394,1492,1586,1686,1779,1874,1972,2063,2154,2238,2343,2451,2550,2656,2768,2871,3037,3135,9407"}}]}]}